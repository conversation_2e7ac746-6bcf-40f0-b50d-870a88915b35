<html>
    <head>
        <title>Activity 17</title>
    </head>

    <body>
        <h1>Activity 17 - March 10, 2025</h1>
        <p>Submitted by <PERSON> </p>
        <hr>

        <h1>All About Date and Time</h1>

        <?php
            echo "<h3>Different Date Formats</h3>";
            echo "1. Today is ".date("m/d/Y")."<br>";
            echo "2. Today is ".date("m/d/y")."<br>";
            echo "3. Today is ".date("M/d/Y")."<br>";
            echo "4. Today is ".date("Y/m/d")."<br>";
            echo "5. Today is ".date("m.d.Y")."<br>";
            echo "1. Today is ".date("m-d-Y")."<br>";
            echo "<hr>";

            echo "<h3> Different Time Formats</h3>";
            echo "1. The time right now is ".date("h:i:s")."<br>";
            echo "2. The time right now is ".date("h:i:sa")."<br>";
            echo "3. The time right now is ".date("h:i:sA")."<br>";
            echo "4. The time right now is ".date("H:i:s")."<br>";
            echo "<hr>";

            echo "<h3>Set Up Timezone </h3>";
            date_default_timezone_set("America/Los_Angeles");
            echo "After setting my time zone to Los Angeles, the time now is ".date("h:i:sa")."<br>";
            date_default_timezone_set("America/New_York");
            echo "After setting my time zone to New York, the time now is ".date("h:i:sa")."<br>";
            echo "<hr>";

            echo "<h3>Different Formats for Days in a Week</h3>";
            echo "What day is today? Today is ".date("l")."<br>"; // lower case L, not number 1
            echo "What day is today? Today is ".date("D")."<br>";
            echo "What day is today? Today is ".date("w")."<br>";
            echo "<hr>";

            echo "<h3>Create Dates Using String</h3>";
            $dbstr = "March 21 1997";
            $bd = strtotime($dbstr);
            echo "My birthday is ".$dbstr."<br>";
            echo "What day is my birthday? It is ".date("l", $bd)."<br>";
            echo "<hr>";

            echo "<h3>Making Time</h3>";
            $anotherday = mktime(12, 0, 0, 1,1,2000);
            echo "This is the number of seconds since 12am 1/1/1970 to 12pm 1/1/2000: ".$anotherday."<br>";
            echo "The created time is: ".date("Y-m-d h:i:sa", $anotherday)."<br>";
            $x = strtotime("tomorrow");
            echo "Tomorrow is: ".date("Y-m-d h:i:sa", $x)."<br>";
            $x = strtotime("+3 months", time());
            echo "3 months after tomorrow will be ".date("m/d/Y", $x)."<br>";
            $x = strtotime("-3 months", time());
            echo "3 months before tomorrow was ".date("m/d/Y", $x)."<br>";
            $x = strtotime("3/5/2025");
            echo "The time we created is ".date("Y-m-d h:i:sa", $x)."<br>";
            echo "<hr>";

            echo "<h3>Counting Down Example</h3>";
            $specialDay = strtotime("Feb 8 2026"); // Superbowl Feb 9 2025
            $today = strtotime("Now");
            $diff = $specialDay - $today; // in seconds
            $nDays = ceil($diff/60/60/24); // days
            if($diff < 0) {
                echo "This event already happened ".abs($nDays)." days before today.";
            } else {
                echo "There are ".$nDays." days until the big event!";
            }

            echo "<h3>Final Exam Count Down</h3>";
            //Final Exam is May 5, 2025 at 8am
            //$finalExam = mktime(8, 0, 0, 5, 5, 2025);
            //echo "Final Exam is on ".date("m/d/Y h:i:sA", $finalExam)."<br>";
            $finalExam = strtotime("May 5 2025");
            $today = strtotime("Now");
            $difference = $finalExam - $today;
            $daysRemaining = ceil($difference/60/60/24);
            if ($difference < 0) {
                echo "The final exam has already passed.";
            } else {
                echo "There are ".$daysRemaining." days left until the final exam!";
            }
        ?>

    </body>
</html>