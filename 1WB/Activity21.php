<html>
    <head>
        <title>Activity 21</title>
    </head>

    <body>
        <h1>Activity 21 - March 31, 2025</h1>
        <p>Submitted by <PERSON> </p>
        <hr>

        <h1>Introducing <PERSON></h1>

        <div id = "mydoc">
            <h2>Ajax Example 1: Loading a very long article</h2>
            <p>This is an article about topic so and so...</p>
            <button type = "button" onclick = "LoadDoc(); ">Click here to read more</button>
        </div>

        <script>
            function LoadDoc() {
                var xhttp = new XMLHttpRequest();
                xhttp.onreadystatechange = function() {
                    if(this.readyState == 4 && this.status == 200)
                        document.getElementById("mydoc").innerHTML = this.responseText;
                } // 0: request not init
                // 1: server connection established
                // 2: request received
                // 3: processing request
                // 4: request finished and response is ready
                // 200: OK
                // 404: not found

                xhttp.open("GET", "AjaxFile.txt", true);
                xhttp.send();
            }
        </script>

        <hr>
        <h2>Ajax Example 2: Update Price</h2>
        <h3>The most recent stock price for AAPL is $<span id = "myprice">201.58</span></h3>

        <script>
            var alarm = setInterval(LoadPrice, 500); // call LoadPrice every 500 millisec
            function LoadPrice() {
                var xhttp = new XMLHttpRequest();
                xhttp.onreadystatechange = function() {
                    if(this.readyState == 4 && this.status == 200)
                        document.getElementById("myprice").innerHTML = this.responseText;
                }

                xhttp.open("GET", "AjaxAction1.php", true);
                xhttp.send();
            }
        </script>

        <hr>
        <h2>Ajax Example 3: Using the Progress Bar when loading something that takes a long time</h2>
        <h3>Progress Make: <span id = "pvalue"> 0 </span> % </h3>
        <progress min = "0" max = "100" value = "0" id = "myprogress" style = "display: none;"></progress>
        <button type = "button" onclick= "LoadInfo();">Start to Load</button>

        <script>
            function LoadInfo() {
                var alarm2 = setInterval(showProgress, 300); // call showProgress every 300 millisec
            }
            function showProgress() {
                var xhttp = new XMLHttpRequest();
                xhttp.onreadystatechange = function() {
                    if(this.readyState == 4 && this.status == 200)
                        document.getElementById("pvalue").innerHTML = this.responseText;
                }

                var p = document.getElementById("pvalue").innerHTML;
                xhttp.open("GET", "AjaxAction2.php?cp="+p, true);
                xhttp.send();

                if(p >= 100)
                    clearInterval(alarm2);

                document.getElementById("myprogress").style.display = "block";
                document.getElementById("myprogress").value = p;
            }
        </script>



    </body>
</html>