<?php
    ob_start();
    ini_set('display_errors', 1);
    ini_set('display_startup_errors', 1);
    error_reporting(E_ALL);

    session_start();

    function test_input($data) {
        $data = trim($data);
        $data = stripslashes($data);
        $data = htmlspecialchars($data);
        return $data;
    }

    include "connection.php";

    if ($_SERVER["REQUEST_METHOD"] == "POST") {
        $email = mysqli_real_escape_string($dbc, test_input($_POST['email']));
        $password = test_input($_POST['password']);

        $query = "SELECT * FROM users WHERE email='$email'";
        $result = mysqli_query($dbc, $query);

        if (mysqli_num_rows($result) == 1) {
            $user = mysqli_fetch_assoc($result);

            if ($password === $user['pw']) {
                $_SESSION['id'] = $user['id'];
                $_SESSION['type'] = $user['type'];

                //echo "User Type: " . $user['type'];

                switch ($user['type']) {
                    case 0:
                        header("Location: admin_home.php");
                        break;
                    case 1:
                        header("Location: v_home.php");
                        break;
                    case 2:
                        header("Location: s_home.php");
                        break;
                    default:
                        echo "Invalid user type!";
                        break;
                }
                exit();
            } else {
                echo "Invalid password!";
            }
        } else {
            echo "User not found!";
        }
    }
    mysqli_close($dbc);
?>

<?php
    include 'nav.php';
?>

<html>
    <head>
        <link rel="stylesheet" type="text/css" href="apath.css">
        <title>Login</title>
    </head>

    <body>
        <div class="container">
            <p>Login with the email you used during registration.</p>

            <form method="POST" action="">
                <input type="email" name="email" placeholder="Email" required><br>
                <input type="password" name="password" placeholder="Password" required><br>

                <div class="checkbox-container">
                    <input type="checkbox" name="remember"> Remember me
                    <a href="#">Forgot password?</a>
                </div>

                <br>
                <button type="submit">Login</button>
            </form>

            <br>
            <p>No Account? <a href="signup.php">Create One</a></p>
            <p>Covid information and guidelines</p>
        </div>
    </body>
</html>