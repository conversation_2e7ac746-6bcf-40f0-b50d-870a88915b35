<?php
    session_start();
    include 'connection.php';

    if ($_SERVER["REQUEST_METHOD"] == "POST") {
        $email = mysqli_real_escape_string($dbc, trim($_POST['email']));
        $password = trim($_POST['password']);
        $user_type = isset($_POST['user_type']) ? (int)$_POST['user_type'] : 0;

        if (empty($email) || empty($password) || $user_type === 0) {
            echo "Please fill out all fields!";
        } elseif (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
            echo "Invalid email format.";
        } else {
            $query = "SELECT * FROM users WHERE email='$email'";
            $result = mysqli_query($dbc, $query);

            if (mysqli_num_rows($result) > 0) {
                echo "Email already exists. Please use a different email.";
            } else {
                $insert_query = "INSERT INTO users (email, pw, type) VALUES ('$email', '$password', $user_type)";
                if (mysqli_query($dbc, $insert_query)) {
                    echo "Account created successfully! <a href='login.php'>Login here</a>.";
                } else {
                    echo "Error: " . mysqli_error($dbc);
                }
            }
        }
        mysqli_close($dbc);
    }
?>

<?php
    include 'nav.php';
?>

<html>
    <head>
        <link rel="stylesheet" type="text/css" href="apath.css">
        <title>Sign Up</title>
    </head>

    <body>
        <div class="container">
            <p>We are going to communicate with you using email often.<br>
                Please create your new account with your most frequently used email.</p>

            <form method="POST" action="">
                <input type="email" name="email" placeholder="Email"><br>
                <input type="password" name="password" placeholder="Password"><br>

                <input type="radio" name="user_type" value="1" required> I am signing up as a volunteer <br>
                <input type="radio" name="user_type" value="2" required> I am an international student that needs help <br>

                <br>
                <button type="submit">Create Account</button>
            </form>

            <br>
            <p>Already Have An Account? <a href="login.php">Login</a></p>
            <p>Covid information and guidelines</p>
        </div>
    </body>
</html>