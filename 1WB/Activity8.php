<html>
    <head>
        <title>Activity 8</title>
        
        <style>
            .error { color: #FF0000; }
        </style>
    </head>

    <body>
        <h1>Activity 8 - Jan 29, 2025; updated on Jan 31</h1>
        <p>Submitted by <PERSON> </p>

        <h2>Major Updated</h2>
        <ul>
            <li>Create database and table in phpmyadmin (done)</li>
            <li>Add password field</li>
            <li>Create connection.php (only need to do it once)</li>
            <li>Insert form information to the table</li>
        </ul>
        
        <hr>

        <h2>Form Example 3: Using Post Method, Trigger SELF</h2>

        <?php
            $firstnameErr = $lastnameErr = $phoneErr = $emailErr = $genderErr = $levelErr = $passwordErr = "";
            
            $firstname = "Firstname"; // default value
            $lastname = "Lastname"; // default value
            $phone = "************"; // default value
            $email = "<EMAIL>"; // default value
            
            $gender = $level = $password1 = $password2 = "";

            $flag = 0; //no red flag; ready to insert
            
            if ($_SERVER["REQUEST_METHOD"] == "POST") {
                $firstname = test_input($_POST["firstname"]);
                $lastname = test_input($_POST["lastname"]);
                $phone = test_input($_POST["phone"]);
                $email = test_input($_POST["email"]);
                $gender = test_input($_POST["gender"] ?? "");
                $password1 = test_input($_POST["password1"]);
                $password2 = test_input($_POST["password2"]);
                $level = test_input($_POST["level"] ?? "");

                //FIRST NAME
                if ($firstname == "" || $firstname == "Firstname") {
                    $firstnameErr = "Firstname is required!";
                    $flag = 1;
                } else {
                    if (!preg_match("/^[a-zA-Z ]*$/",$firstname)) {
                        $firstnameErr = "Only letters and white space allowed!";
                        $flag = 2;
                    }
                }
                
                //LAST NAME
                if ($lastname == "" || $lastname == "Lastname") {
                    $lastnameErr = "Lastname is required!";
                    $flag = 3;
                } else {
                    if (!preg_match("/^[a-zA-Z ]*$/",$lastname)) {
                        $lastnameErr = "Only letters and white space allowed!";
                        $flag = 4;
                    }
                }
                
                //PHONE
                if ($phone == "" || $phone == "************") {
                    $phoneErr = "Phone number is required!";
                    $flag = 5;
                } else {
                    if (!preg_match("/^[0-9]{3}-[0-9]{3}-[0-9]{4}$/", $phone)) {
                        $phoneErr = "Invalid phone number!";
                        $flag = 6;
                    }
                }

                //EMAIL
                if ($email == "") {
                    $emailErr = "Email is required!";
                    $flag = 7;
                } elseif ($email == "<EMAIL>") {
                    $emailErr = "Please enter a valid email!";
                    $flag = 8;
                } else {
                    if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
                        $emailErr = "Invalid email format.";
                        $flag = 9;
                    }
                }

                //PASSWORD
                if ($password1 == "") {
                    $passwordErr = "Password is required!";
                    $flag = 10;
                } else {
                    if ($password1 != $password2) {
                        $passwordErr = "Password does not match!";
                        $flag = 11;
                    }
                }

                //GENDER
                if ($gender == "") {
                    $genderErr = "Gender is required!";
                    $flag = 12;
                }

                //LEVEL
                if ($level == "") {
                    $levelErr = "Number of credit is required!";
                    $flag = 13;
                }

                // --------------> GET READY TO INSERT INTO DATABASE <--------------
                if ($flag == 0) {
                    include "connection.php";

                    // logic for email
                    $sqs1 = "SELECT * FROM users WHERE email = '$email'";
                    $qresult1 = mysqli_query($dbc, $sqs1);
                    $num1 = mysqli_num_rows($qresult1);

                    // logic for phone number
                    $sqs2 = "SELECT * FROM users WHERE phone = '$phone'";
                    $qresult2 = mysqli_query($dbc, $sqs2);
                    $num2 = mysqli_num_rows($qresult2);

                    // $num should be 0; if not, indicating it is already in the database
                    if ($num1 != 0) {
                        echo "<h3> Email has been used! Please try a different email.</h3>";
                    } elseif ($num2 != 0) {
                        echo "<h3> Phone number has been used! Please try a different phone number.</h3>";
                    } else {
                        $sqs = "INSERT INTO users (firstname, lastname, phone, email, gender, level, pw) 
                        VALUES ('$firstname', '$lastname', '$phone', '$email', '$gender', '$level', '$password1')";

                        mysqli_query($dbc, $sqs);
                        $registered = mysqli_affected_rows($dbc);
                        
                        echo $registered." User information has been stored successfully! <br>";
                        header("Location: registration_success.php"); // Added this line on Feb 5
                    }
                }

            } // END OF POST METHOD IF STATEMENT

            function test_input($data) {
                $data = trim($data);
                $data = stripslashes($data);
                $data = htmlspecialchars($data);
                return $data;
              }
        ?>
        
        <form action = "<?php echo htmlspecialchars($_SERVER["PHP_SELF"]);?>" method = "POST">
            Firstname:
            <input type = "text" name = "firstname" value = "<?php echo $firstname; ?>">
            <span class = "error">* <?php echo $firstnameErr; ?> </span>

            <br><br>

            Lastname:
            <input type = "text" name = "lastname" value = "<?php echo $lastname; ?>">
            <span class = "error">* <?php echo $lastnameErr; ?> </span>
            
            <br><br>

            Phone:
            <input type = "text" name = "phone" value = "<?php echo $phone; ?>">
            <span class = "error">* <?php echo $phoneErr; ?> </span>
            
            <br><br>

            Email:
            <input type = "text" name = "email" value = "<?php echo $email; ?>">
            <span class = "error">* <?php echo $emailErr; ?> </span>
            
            <br><br>

            Password:         <input type = "password" name = "password1" maxlength = "30">
            <span class = "error">* <?php echo $passwordErr; ?> </span> 
            
            <br><br>
            
            Confirm Password: <input type = "password" name = "password2" maxlength = "30">
            <span class = "error">* <?php echo $passwordErr; ?> </span>

            <br><br>

            Gender:
            <span class = "error">* <?php echo $genderErr; ?> </span> 
            <br>

            <input type = "radio" name = "gender" value = "Female"
            <?php if (isset($gender) && $gender == "Female") echo "checked"; ?>
            > Female
            
            <input type = "radio" name = "gender" value = "Male"
            <?php if (isset($gender) && $gender == "Male") echo "checked"; ?>
            > Male
            
            <input type = "radio" name = "gender" value = "Other"
            <?php if (isset($gender) && $gender == "Other") echo "checked"; ?>
            > Other
            
            <br><br>

            The total number of IT credits you have earned:
            <span class = "error">* <?php echo $levelErr; ?> </span> <br>
            
            <input type = "radio" name = "level" value = "A"
            <?php if (isset($level) && $level == "A") echo "checked"; ?>
            > Less than 30 hours <br>

            <input type = "radio" name = "level" value = "B"
            <?php if (isset($level) && $level == "B") echo "checked"; ?>
            > More than 30 but less than 60 hours <br>
            
            <input type = "radio" name = "level" value = "C"
            <?php if (isset($level) && $level == "C") echo "checked"; ?>
            > More than 60 but less than 90 hours <br>

            <input type = "radio" name = "level" value = "D"
            <?php if (isset($level) && $level == "D") echo "checked"; ?>
            > More than 90 hours

            <br><br>

            <input type = "submit">
        </form>

        <hr>
        
        <h3>Testing Area: For Developer Only</h3>
        <?php
            echo "Data collected from the form: <br>";
            echo "<br> First Name: ".$firstname;
            echo "<br> Last Name: ".$lastname;
            echo "<br> Phone: ".$phone;
            echo "<br> Email: ".$email;
            echo "<br> Password1: ".$password1;
            echo "<br> Password2: ".$password2;
            echo "<br> Level: ".$level;
            echo "<br> Flag = ".$flag;
            //echo "<br> SQL statement is ".$sqs;
        ?>

    </body>
</html>