<html>
    <head>
        <title>Activity 4</title>
    </head>

    <body>
        <h1>Activity 4 - Jan 15, 2025 </h1>
        <p>Submitted by <PERSON> </p>
        <hr>

       <h2>Variable Scope</h2>
       <?php
            //global variable
            $x = 10;
            echo "The value of my global Variable x is ".$x."<br>";

            function myFunction() {
                //access the global variable insde a function
                echo "Access the global variable x inside a function".$x;
                echo " will generate a warning message. <br>";

                global $x; //to specify we are access the global variable
                echo "Access global variable x after specified, and the value is ".$x."<br>";
                
                //local variable
                $y = 20;
                echo "The value of the local variable y inside myFunction() is ".$y."<br>";
            }

            myFunction();
            
            echo "Access the local variable y outside the myFunction()".$y;
            echo " will generate a warning message. <br>";
       ?>
       <hr>

       <h2>Using Loop to Build Tables</h2>
       <h3>Table 1</h3>
       <?php
            echo "You can access global variables from another section of the php. <br>";
            echo "The value for variable x is ".$x."<br>";

            $rowSize = 8;
            $colSize = 8;
            $alternate = 0;

            echo "<table border = '1' style = 'width: 20%;
            margin: auto;
            '>";
            for ($i = 0; $i < $rowSize; $i++) {
                echo "<tr>";
                for ($j = 0; $j < $colSize; $j++) {
                    if ($j%2 == $alternate) {
                        echo "<td style = 'background-color: white;'>";
                    } else {
                        echo "<td style = 'background-color: black;'>";
                    }
                    echo $j;
                    echo "</td>";
                }
                $alternate = !$alternate;
                echo "</tr>";
            }
            echo "</table>";

            //table 2
            echo "<h3>Table 2</h3>";
            echo "<table border = '1' style = 'width: 20%;
            margin: auto;
            '>";
            for ($i = 0; $i < $rowSize; $i++) {
                echo "<tr>";
                for ($j = 0; $j < $colSize; $j++) {
                    if ($j%2 != $alternate) {
                        echo "<td style = 'background-color: white;'>";
                    } else {
                        echo "<td style = 'background-color: black;'>";
                    }
                    echo $j;
                    echo "</td>";
                }
                $alternate = !$alternate;
                echo "</tr>";
            }
            echo "</table>";
       ?>
       <hr>

       <h3>Table 3</h3>
       <?php
            $rowSize = 18;
            $colSize = 18;

            echo "<table border = '1' style = 'width: 40%; margin: auto;'>";
            for ($i = 0; $i < $rowSize; $i++) {
                echo "<tr>";
                for ($j = 0; $j < $colSize; $j++) {
                    if (($i + $j) % 3 == 0) {
                        echo "<td style = 'background-color: red;'>";
                    } else if (($i + $j) % 3 == 1) {
                        echo "<td style = 'background-color: green;'>";
                    }
                    else {
                        echo "<td style = 'background-color: blue;'>";
                    }
                    echo "&nbsp";
                    echo "</td>";
                }
                echo "</tr>";
            }
            echo "</table>";

            //table 4
            echo "<h3>Table 4: Four Colors RGBY</h3>";
            echo "<table border = '1' style = 'width: 40%; margin: auto;'>";
            for ($i = 0; $i < $rowSize; $i++) {
                echo "<tr>";
                for ($j = 0; $j < $colSize; $j++) {
                    if (($i + $j) % 4 == 0) {
                        echo "<td style = 'background-color: red;'>";
                    } else if (($i + $j) % 4 == 1) {
                        echo "<td style = 'background-color: green;'>";
                    }
                    else if (($i + $j) % 4 == 2) {
                        echo "<td style = 'background-color: blue;'>";
                    }
                    else {
                        echo "<td style = 'background-color: yellow;'>";
                    }
                    echo "&nbsp";
                    echo "</td>";
                }
                echo "</tr>";
            }
            echo "</table>";

            //table 5
            echo "<h3>Table 5: Any Number of Colors</h3>";
            $n = 6; //number of colors
            $myColor = array("red", "green", "blue", "yellow", "cyan", "pink");

            echo "<table border = '1' style = 'width: 40%; margin: auto;'>";
            
            for ($i = 0; $i < $rowSize; $i++) {
                echo "<tr>";
                for ($j = 0; $j < $colSize; $j++) {
                    /*
                    if (($i + $j) % 3 == 0) {
                        echo "<td style = 'background-color: red;'>";
                    } else if (($i + $j) % 3 == 1) {
                        echo "<td style = 'background-color: green;'>";
                    }
                    else {
                        echo "<td style = 'background-color: blue;'>";
                    }
                    */
                    for ($k = 0; $k < $n; $k++) {
                        if (($i + $j) % $n == $k) {
                            echo "<td style = 'background-color: ".$myColor[$k]."'>";
                        }
                    }
                    echo "&nbsp";
                    echo "</td>";
                }
                echo "</tr>";
            }
            echo "</table>";

       ?>
    
    </body>
</html>