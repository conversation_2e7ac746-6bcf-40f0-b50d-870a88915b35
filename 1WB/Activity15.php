<html>
    <head>
        <title>Activity 15</title>

        <style>
            .error { color: #FF0000; }
        </style>
    </head>

    <body>
        <h1>Activity 15 - February 26, 2025</h1>
        <p>Submitted by <PERSON>-<PERSON> </p>
        <hr>

        <h1>Open and process file already in the Server</h1>

        <?php
            $file = "Students-Grades-Info.txt";
            $studentStr = file_get_contents($file);
            $studentStr = trim($studentStr);
            $studentList = explode("\n", $studentStr); // 1D array for each studentList
            $nFound = 0;

            foreach ($studentList as $index => $student) {
                $studentInfo[$index] = explode("\t", $student); // 2D array
            }

            //TABLE ONE
            echo "Display file/2D array as a table";
            echo "<table border = '1'>";
            echo "<tr>";
                echo "<td>No. </td>
                        <td>Name </td> 
                        <td>Email </td>
                        <td>Major </td>
                        <td>Grade </td>
                        <td>IP </td>";
            echo "</tr>";

            foreach ($studentInfo as $index => $student) {
                echo "<tr>";
                    echo "<td> ".($index + 1)." </td>"; //echo "<td> ".$index." </td>";
                    foreach ($student as $info) {
                        echo "<td> ".$info." </td>";
                    }
                echo "</tr>";
            }
            echo "</table>";

            //TABLE TWO
            echo "<hr>";
            echo "<h2> Removing Duplicates </h2>";

            echo "<table border = '1'>";
            echo "<tr>";
                echo "<td>No. </td>
                        <td>Name </td> 
                        <td>Email </td>
                        <td>Major </td>
                        <td>Grade </td>
                        <td>IP </td>";
            echo "</tr>";

            foreach ($studentInfo as $index => $student) {
                $ip = $student[4];
                $repeating = false;
                foreach ($studentInfo as $i => $person) {
                    if ($ip == $person[4] && $index > $i) {
                        $repeating = true;
                        break;
                    }
                    if ($index <= $i)
                        break;
                } // end of inner foreach
                if (!$repeating) {
                    $nFound++;
                    echo "<tr>";
                        echo "<td> ".$nFound." </td>";
                        foreach ($student as $info) {
                            echo "<td> ".$info." </td>";
                        }
                    echo "</tr>";
                }
            } // end of outer foreach
            echo "</table>";

            //TABLE THREE
            echo "<hr>";
            echo "<h2> Removing Duplicate Names </h2>";

            echo "<table border = '1'>";
            echo "<tr>";
                echo "<td>No. </td>
                        <td>Name </td> 
                        <td>Email </td>
                        <td>Major </td>
                        <td>Grade </td>
                        <td>IP </td>";
            echo "</tr>";

            $uniqueNames = [];
            $nFound = 0;

            foreach ($studentInfo as $index => $student) {
                $name = $student[0];
                if (!in_array($name, $uniqueNames)) {
                    $uniqueNames[] = $name;
                    $nFound++;
                    echo "<tr>";
                        echo "<td> ".$nFound." </td>";
                        foreach ($student as $info) {
                            echo "<td> " . $info . " </td>";
                    }
                    echo "</tr>";
            }
        }
        echo "</table>";

        ?>

    </body>
</html>