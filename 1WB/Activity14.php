<html>
    <head>
        <title>Activity 14</title>

        <style>
            .error { color: #FF0000; }
        </style>
    </head>

    <body>
        <h1>Activity 14 - February 19, 2025</h1>
        <p>Submitted by <PERSON> </p>
        <hr>

        <h1>Upload Files to Server</h1>

        <form method = "post" action = "<?php echo htmlspecialchars($_SERVER["PHP_SELF"]);?>"
            enctype = "multipart/form-data">
            Select an image to upload: <input type = "file" name = "myimage"> <br> <br>
            Select a PDF file to upload: <input type = "file" name = "mypdf"> <br> <br>

            <input type = "submit" name = "submit" value = "UPLOAD">
        </form>


    </body>
</html>

<?php
    if (isset($_POST["submit"])) {
        // upload the image
        $tagName = "myimage";
        $fileAllowed = "PNG:JPEG:JPG:GIF:BMP";
        $sizeAllowed = 1000000; // about 10MB
        $overwriteAllowed = 1;

        $file = uploadFile($tagName, $fileAllowed, $sizeAllowed, $overwriteAllowed);

        if ($file != false) {
            //display the image <img src = "fileName.extension">
            echo "<img src = '".$file."' width = '300'>";
        } else {
            echo "Sorry, uploading of the image failed. <br>";
        }

        // upload the PDF
        $tagName = "mypdf";
        $fileAllowed = "pdf:PDF";
        $sizeAllowed = 1000000; // about 10MB
        $overwriteAllowed = 1;

        $file = uploadFile($tagName, $fileAllowed, $sizeAllowed, $overwriteAllowed);

        if ($file != false) {
            // display the pdf
            echo "<hr> <h2>Three different ways to display PDF file</h2>";
            echo "1. Using Hyperlink <br>";
            echo "<a href = '".$file."' target = '_blank'> Click Here </a> to open the PDF file";

            echo "<br>";

            echo "<br> 2. Use iframe tag. <br>";
            echo "<iframe src = '".$file."' width = '500' height = '600'> </iframe>";

            echo "<br>";

            echo "<br> 3. Use embed tag. <br>";
            echo "<embed type = 'application/pdf' src = '".$file."' width = '500'>";

            echo "<br>";

        } else {
            echo "Sorry, uploading of the PDF failed.";
        }

    }

    function uploadFile($tagName, $fileAllowed, $sizeAllowed, $overwriteAllowed)
    {
        $uploadOk = 1; // yes, it is ok to upload
        $dir = "upload/"; // this is the folder in the server
        $file = $dir . basename($_FILES[$tagName]["name"]);
        $fileType = pathinfo($file, PATHINFO_EXTENSION); // destination and file name

        $fileSize = $_FILES[$tagName]["size"];

        if ($fileSize > $sizeAllowed) {
            echo "File size is too big. Maximum 9MB";
            $uploadOk = 0; // not ok to upload
        }

        if (!stristr($fileAllowed, $fileType)) {
            echo "This file type is not allowed";
            $uploadOk = 0;
        }

        if (file_exists($file) && !$overwriteAllowed) {
            echo "File already exists. Please upload a different file.";
            $uploadOk = 0;
        }

        if ($uploadOk == 1) {
            if (!move_uploaded_file($_FILES[$tagName]["tmp_name"], $file)) {
                echo "Sorry, uploading failed in the process.";
                $uploadOk = 0;
            }
        }

        if ($uploadOk == 1) {
            return $file;
        } else {
            return false;
        }
    }
?>