<html>
    <head>
        <title>Activity 6</title>
    </head>

    <body>
        <h1>Activity 6 - Jan 22, 2025 </h1>
        <p>Submitted by <PERSON> </p>
        <hr>

        <h2>2D Array</h2>
        <?php
            $students = array (
                array("<PERSON>", 22, "<PERSON>", "9001234"),
                array("<PERSON>", 20, "Female", "9001235"),
                array("<PERSON>", 18, "Male", "9001236"),
                array("<PERSON>", 21, "Female", "9001237"),
            );

            for ($row = 0; $row < count($students); $row++) {
                echo "<p> Student ".($row + 1)." </p>";
                echo "<ul>";
                for ($col = 0; $col < count($students[$row]); $col++) {
                    echo "<li>".$students[$row][$col]."</li>";
                }
                echo "</ul>";
            }

            //table
            echo "<h3> Display 2D Array in Table </h3>";
            echo "<table border= '1' style = 'width: 40%; margin: auto;'>";
            echo "<tr>
                    <td> Name </td>
                    <td> Age </td>
                    <td> Gender </td>
                    <td> ID </td>
                    </tr>";
            foreach ($students as $row => $person) {
                echo "<tr>";
                foreach ($person as $value) {
                    echo "<td>";
                    echo "$value";
                    echo "</td>";
                }
                echo "</tr>";
            }
            echo "</table>";


            echo "<h3> Array Searching </h3>";
            echo "<p> Searching through the 2D arry to find Jenny </p>";
            $name = "Jenny";
            foreach ($students as $person) {
                if ($name == $person[0]) {
                    echo "Name: ".$person[0];
                    echo "<br> Age: ".$person[1];
                    echo "<br> Gender: ".$person[2];
                }
            }

            $students[10] = array("Jason", 22, "Male", 9002123);
            $students[11] = array("Megan", 18, "Female", 9002124);
            
            echo "<p> Searching for the person with the youngest age.</p>";
            $youngest = $students[0][1];
            foreach ($students as $index => $person) {
                if ($person[1] < $youngest)
                $youngest = $person[1];
            }
            echo "The youngest age is: ".$youngest."<br>";

            //task - show all the people with the youngest age and show how many
            $count = 0;
            foreach ($students as $person) {
                if ($person[1] == $youngest) {
                    $count++;
                    echo "Student ".$person[0]." age is ".$person[1]." and is a ".$person[2]."<br>";
                }
            }
            echo "Total ".$count." students has been found with age ".$youngest."<br>";

            //task - Male students
            echo "<p> Find all the male students. Display their names, age, and the total number. </p>";
            $maleCount = 0;
            foreach ($students as $person) {
                if ($person[2] == "Male") {
                    $maleCount++;
                    echo "Student Name: ".$person[0].", Age: ".$person[1]."<br>";
                }
            }
            echo "Total number of male students: ".$maleCount."<br>";

            //task - Female students
            echo "<p> Find all the female students less than or equal to 20 years of age.</p>";
            echo "<p> Display thier name, age, and the total number. </p>";
            $femaleCount = 0;
            foreach ($students as $person) {
                if ($person[2] == "Female" && $person[1] <= 20) {
                    $femaleCount++;
                    echo "Student Name: ".$person[0].", Age: ".$person[1]."<br>";
                }
            }
            echo "Total number of female students less than or equal to 20 years of age: ".$femaleCount."<br>";
        ?>
    </body>
</html>