<html>
    <head>
        <title>Activity 3</title>
    </head>

    <body>
        <h1>Activity 3 - Jan 13, 2025 </h1>
        <p>Submitted by <PERSON> </p>
        <hr>

        <h2>Functions Defined in a Different File</h2>
        <h3>Example 1: Creating a Table</h3>
        <?php
            include "lib01.php";
            
            echo "<table style = 'background-color: blue;
            color: blue;
            width: 50%;
            margin: auto;
            '>";

            echo "<tr>";
            echo "<td>";
                echo "<div style = 'width: 50%;
                margin: auto;
                background-color: blue;
                text-align: center;
                color: red;
                '>";
            drawTrapezoid(1, 10, "*");
                echo "</div>";
            echo "</td>";

            echo "<td>";
                echo "<div style = 'width: 50%;
                margin: auto;
                background-color: blue;
                text-align: center;
                color: red;
                '>";
            drawTrapezoid(1, 10, "*");
                echo "</div>";
            echo "</td>";

            echo "</tr>";
            echo "</table>";
        ?>
    
        <h3>Example 2: Show message</h3>
        <?php
            showMessage();
            echo "Show Message with Looping <br>";
            for ($i = 0; $i < 20; $i++) {
                echo "<span style = 'font-size: ".($i*2+10).";
                color: rgba(".($i*10).", 0, 0, 1);
                '>";
                //echo "<span style = 'font-size:".($i*2)."'>";
                echo "Message".($i + 1).":";
                showMessage();
                echo "</span>";
            }
        ?>
        <hr>

        <h3>Example 3: Display Image</h3>
        <?php
            //display image based on temperature
            $t = rand(10, 100);
            echo "The temperature today is ".$t." degrees.<br>";
            if ($t < 40) {
                showImage("Freezing");
            }
            else if ($t < 55) {
                showImage("Cold");
            }
            else if ($t < 80) {
                showImage("Warm");
            }
            else {
                showImage("Hot");
            }
        ?>

    </body>
</html>