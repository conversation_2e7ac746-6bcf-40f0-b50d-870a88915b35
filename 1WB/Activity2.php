<html>
    <body>
        <h1>Activity 2 - Jan 10, 2025 </h1>
        <p>Submitted by <PERSON> </p>

        <hr>
        <h2>While Loop</h2>

        <?php
            $x=1;
            while ($x<=5) {
                echo "The value for x is ". $x . "<br>";
                $x++;
            }
            echo "The final value for x after the while loop is ".$x."<br>";

            //for loop
            echo "<h2> For Loop </h2>";
            echo "The first for loop example <br>";
            for ($x=0; $x<100; $x+=10) {
                echo "The value for x is ".$x . "<br>";
            }
            echo "The final value for x after the for loop is ".$x."<br>";

            //2nd for loop
            echo "<br> The 2nd for loop example is to caluculate 1+2+3+...+100 <br>";
            $sum = 0;
            for ($x=1; $x<=100; $x++) {
                echo $x. " ";
                $sum += $x;
            }
            echo "<br> The sum of 1+2+3+...+100 = ".$sum."<br>";
            
            
            //nested loop
            echo "<h2> Nested Looping </h2>";
            for ($row = 0; $row < 5; $row++) {
                for ($x = 0; $x < 10; $x++) {
                    echo "*";
                }
                echo "<br>";
            }
            
            echo "The 2nd nested looping example <br>";
            for ($row = 0; $row < 5; $row++) {
                for ($x = 0; $x <= $row; $x++) {
                    echo "*";
                }
                echo "<br>";
            }


            echo "The 3rd nested looping example <br>";
            for ($row = 5; $row <= 10; $row++) {
                for ($x = 0; $x <= $row; $x++) {
                    echo "*";
                }
                echo "<br>";
            }

        ?>
        
    <hr>
    <h2>Functions</h2>

        <?php
            function drawTrapezoid($top, $bottom, $symbol) {
                for ($row = $top -1; $row < $bottom; $row++) {
                    for ($x = 0; $x < $row + 1; $x++) {
                        echo $symbol;
                    }
                    echo "<br>";
                }
            }

            drawTrapezoid(3, 8, "#");
            echo "<hr>";
        ?>
    
    <h2>Adding Some Style</h2>
        <?php
            echo "<div style = 'width: 50%;
            margin: auto;
            background-color: blue;
            text-align: center;
            color: red;'>";
            drawTrapezoid(1, 10, "&");
            echo "</div>";
            echo "<hr>";
            
            echo "<div style = 'width: 50%;
            margin: auto;
            background-color: blue;
            text-align: center;
            color: red;
            line-height: 0.8;'>";
            drawTrapezoid(1, 10, "&");
            echo "</div>";
            echo "<hr>";
            
            echo "<div style = 'width: 50%;
            margin: auto;
            background-color: blue;
            text-align: right;
            color: red;
            line-height: 0.8;'>";
            drawTrapezoid(1, 10, "&");
            echo "</div>";
            echo "<hr>";

        ?>

    </body>
</html>
