<html>
    <head>
        <title>Activity 18a</title>
        </head>

    <body>
        <h1>Activity 18a - March 12, 2025</h1>
        <p>Submitted by <PERSON> </p>
        <hr>

        <h1>Encryption</h1>
        <ol>
            <li>Plain pw</li>
            <li>md5() function</li>
            <li>Adding salt to md5</li>
            <li>password_hash() function</li>
        </ol>
        <hr>

        <?php
            $pw = "1111"; // usually pw come from user form
            echo "The original pw string is ".$pw."<br>";

            $pw1 = md5($pw);
            echo "The encrypted string with md5() function is ".$pw1."<br>";

            $salt = "<EMAIL>"; // this should be a random string
            $pw2 = md5($salt.$pw);
            echo "The encrypted string with md5 and the salt is ".$pw2."<br>";

            //using hash function for php 5 or later
            $pw3 = password_hash($pw, PASSWORD_DEFAULT);
            echo "The encrypted string using password_hash function is ".$pw3."<br>";
            echo "<h3>The encrypted string is very long.
                    Make sure you update your database pw column, so it has enough space to store the encrypted pw.
                    Length 256 should be enough.</h3>";

            $userInput = "1111"; // this will be user input from the login page
            $verify = password_verify($userInput, $pw3);
            if ($verify) {
                echo "Password verify successfully!";
            } else {
                echo "Your password does not match with our record. Please try again";
            }


        ?>


    </body>
</html>
