<html>
    <head>
        <title>Activity 16</title>
    </head>

    <body>
        <h1>Activity 16 - February 28, 2025</h1>
        <p>Submitted by <PERSON> </p>
        <hr>

        <h1>Open and process file already in the Server</h1>

        <?php
            $file = "Students-Grades-Info.txt";
            $studentStr = file_get_contents($file);
            $studentStr = trim($studentStr);
            $studentList = explode("\n", $studentStr); // 1D array for each studentList
            $nFound = 0;

            foreach ($studentList as $index => $student) {
                $studentInfo[$index] = explode("\t", $student); // 2D array
            }

            echo "<h3>Progress Bar with Student Major</h3>";
            $allMajor = array("Digital Media", "Security", "Business", "Software", "Other");
            $allFound = array(0, 0, 0, 0, 0);

            foreach ($allMajor as $index => $major) {
                foreach ($studentInfo as $student) {
                    if ($major == $student[2])
                        $allFound[$index]++;

                }
            }

            // use progress bar to show summary of student major information
            echo "<table>";
            echo "<tr>";
                echo "<td>Major</td> <td>No. of Students</td> <td>Percentage</td> <td>Bar</td>";
            echo "</tr>";

            foreach ($allMajor as $index => $major) {
                $p = $allFound[$index] / count($studentInfo) * 100;
                $p = round($p, 2);

                echo "<tr>";
                    echo "<td> ".$major." </td>";
                    echo "<td> ".$allFound[$index]." </td>";
                    echo "<td> ".$p." </td>";
                    echo "<td> <progress max = '100' min = '0' value = ".$p."> </progress> </td>";
                echo "</tr>";
            }

            echo "</table>";

            /*
            //TABLE ONE
            echo "Display file/2D array as a table";
            echo "<table border = '1'>";
            echo "<tr>";
                echo "<td>No. </td>
                        <td>Name </td> 
                        <td>Email </td>
                        <td>Major </td>
                        <td>Grade </td>
                        <td>IP </td>";
            echo "</tr>";

            foreach ($studentInfo as $index => $student) {
                echo "<tr>";
                    echo "<td> ".($index + 1)." </td>"; //echo "<td> ".$index." </td>";
                    foreach ($student as $info) {
                        echo "<td> ".$info." </td>";
                    }
                echo "</tr>";
            }
            echo "</table>";
            */

            //TABLE TWO
            echo "<hr>";
            echo "<h2> Removing Duplicates IP Address and save the clean copy in a new file</h2>";

            $myfile1 = fopen("newfile.txt", "w") or die("Unable to open ths file!");

            echo "<table border = '1'>";
            echo "<tr>";
                echo "<td>No. </td>
                        <td>Name </td> 
                        <td>Email </td>
                        <td>Major </td>
                        <td>Grade </td>
                        <td>IP </td>";
            echo "</tr>";

            foreach ($studentInfo as $index => $student) {
                $ip = $student[4];
                $repeating = false;
                foreach ($studentInfo as $i => $person) {
                    if ($ip == $person[4] && $index > $i) {
                        $repeating = true;
                        break;
                    }
                    if ($index <= $i)
                        break;
                } // end of inner foreach
                if (!$repeating) {
                    $nFound++;
                    echo "<tr>";
                        echo "<td> ".$nFound." </td>";
                        fwrite($myfile1, $nFound);
                        fwrite($myfile1, ",");
                        foreach ($student as $ind => $info) {
                            echo "<td> ".$info." </td>";
                            fwrite($myfile1, $info);
                            if ($ind < 4)
                                fwrite($myfile1, ",");
                        }
                        echo "</tr>";
                        fwrite($myfile1, "\n");
                }
            } // end of outer foreach
            echo "</table>";

            //TABLE THREE
            echo "<hr>";
            echo "<h2> Removing Duplicate Names </h2>";

            $myfile2 = fopen("newfile2.txt", "w") or die("Unable to open ths file!");

            //complete this
            //write to Create a clean copy without duplicate names;
            //Open the new file, break it into 2D array;
            //write a Display student information about grade. Display progress bar with students in A, B, C, D or lower;


            echo "<table border = '1'>";
            echo "<tr>";
                echo "<td>No. </td>
                        <td>Name </td> 
                        <td>Email </td>
                        <td>Major </td>
                        <td>Grade </td>
                        <td>IP </td>";
            echo "</tr>";

            $uniqueNames = [];
            $nFound = 0;

            foreach ($studentInfo as $index => $student) {
                $name = $student[0];
                if (!in_array($name, $uniqueNames)) {
                    $uniqueNames[] = $name;
                    $nFound++;
                    echo "<tr>";
                        echo "<td> ".$nFound." </td>";
                        foreach ($student as $info) {
                            echo "<td> " . $info . " </td>";
                    }
                    echo "</tr>";
            }
        }
        echo "</table>";
        ?>

    </body>
</html>