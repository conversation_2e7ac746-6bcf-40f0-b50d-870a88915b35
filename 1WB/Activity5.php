<html>
    <head>
        <title>Activity 5</title>
    </head>

    <body>
        <h1>Activity 5 - Jan 17, 2026</h1>
        <p>Submitted by <PERSON></p>
        <hr>

        <h2>Using Array</h2>
        <?php
            $cars = array("BMW", "<PERSON><PERSON>", "Lamborghini");
            //access array through index 0 to size -1
            
            echo "After graducation, I want to have a ". $cars[0].".<br>";

            // define array using index
            $friends[0] = "<PERSON>";
            $friends[1] = "<PERSON>";
            $friends[2] = "<PERSON>";
            $friends[3] = "<PERSON>";
            $friends[4] = "<PERSON>";

            echo "<p>Use Looping to display arrray values </p>";
            for ($i = 0; $i < count($friends); $i++) {
                echo "Friend number ".($i + 1)." is ".$friends[$i].".<br>";
            }

            echo "<p>Assocative Array</p>";
            //define associative array
            $SID["<PERSON>"] = "9001234";
            $SID["<PERSON>"] = "9001235";
            $SID["Susan"] = "9001236";
            $SID["Jenny"] = "9001237";
            $SID["Josh"] = "9001238";
            echo "<p>Use Looping to display Associative Array values</p>";
            foreach ($SID as $name => $id) {
                echo "Student id of ".$name." is ".$id.".<br>";
            }

            echo "<p>Define another associative array</p>";
            $salary = array("Mike"=>400,
            "Jeff"=>700,
            "Susan"=> 2500,
            "Jack"=>2000,
            "Jenny"=>4000,
            "Josh"=>250);

            $sum = 0;
            foreach ($salary as $name => $payment) {
                $sum += $payment;
            }
            echo "The total budget for salary is $".$sum.".<br>";

            echo "<p>Find the employee with the highest payment </p>";
            $topSalary = $salary["Mike"];
            $topPerson = "Mike";

            foreach ($salary as $name => $payment) {
                if ($payment > $topSalary) {
                    $topSalary = $payment;
                    $topPerson = $name;
                }
            }

            echo $topPerson." has the highest salary, which is $".$topSalary.".<br>";

            //after class - lowest payment
            echo "<p>Find the employee with the lowest payment </p>";
            $lowSalary = $salary["Mike"];
            $lowPerson = "Mike";

            foreach ($salary as $name => $payment) {
                if ($payment < $lowSalary) {
                    $lowSalary = $payment;
                    $lowPerson = $name;
                }
            }

            echo $lowPerson." has the lowest salary, which is $".$lowSalary.".<br>";
        ?>
        <hr>

        <h2>2D Array</h2>
        <?php
            $students = array(
                array("Mike", 22, "Male", "9001234"),
                array("Jeff", 23, "Male", "9001235"),
                array("Susan", 21, "Female", "9001236"),
                array("Jenny", 20, "Female", "9001237"),
                array("Josh", 18, "Male", "9001238")
            );

            for ($row = 0; $row < count($students); $row++) {
                echo "<p> Student ".($row + 1)." </p>";
                echo "<ul>";
                for ($col = 0; $col < count($students[$row]); $col++) {
                    echo "<li>".$students[$row][$col]."</li>";
                }
                echo "</ul>";
            }

            //added student id in array
        ?>

    </body>
</html>