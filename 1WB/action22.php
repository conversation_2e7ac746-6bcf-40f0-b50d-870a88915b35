<?php
    session_start();
?>

<html>
    <body>
        <?php
            if(isset($_POST["submit"]))
            {
                // for PC
                if($_POST["PC"] == "PC") {
                    $_SESSION["PC"] = "PC";
                    if(isset($_SESSION["nPC"]))
                        $_SESSION["nPC"] += $_POST["nPC"]; // for continue shopping
                    else // this is the first time
                    $_SESSION["nPC"] = $_POST["nPC"];
                }

                // for iPad
                if($_POST["Pad"] == "Pad") {
                    $_SESSION["Pad"] = "Pad";
                    if(isset($_SESSION["nPad"]))
                        $_SESSION["nPad"] += $_POST["nPad"]; // for continue shopping
                    else // this is the first time
                        $_SESSION["nPad"] = $_POST["nPad"];
                }

                // for Mac
                if($_POST["Mac"] == "Mac") {
                    $_SESSION["Mac"] = "Mac";
                    if(isset($_SESSION["nMac"]))
                        $_SESSION["nMac"] += $_POST["nMac"]; // for continue shopping
                    else // this is the first time
                        $_SESSION["nMac"] = $_POST["nMac"];
                }
            }

            $total = 0;

            echo "  <h1> Your shopping cart: </h1>
                    <hr>
                    <table>
                        <tr>
                            <td>Item</td>
                            <td>Price</td>
                            <td>Quantity</td>
                        </tr>
            ";

            // handle PC
            if($_SESSION["PC"] == "PC") {
                echo "<br> Calculate Total: Price = ".$_POST["PCPrice"]." * ".$_SESSION["nPC"];
                $total += $_POST["PCPrice"] * $_SESSION["nPC"];

                echo "<tr> <td> <img src = '".$_POST["PCImage"]."' width = '120'> </td>";
                echo "<td> ".$_POST["PCPrice"]." </td>";
                echo "<td> ".$_SESSION["nPC"]." </td> ";
                echo "</tr>";
            }

            // handle iPad
            if($_SESSION["Pad"] == "Pad") {
                echo "<br> Calculate Total: Price = ".$_POST["PadPrice"]." * ".$_SESSION["nPad"];
                $total += $_POST["PadPrice"]*$_SESSION["nPad"];

                echo "<tr> <td> <img src = '".$_POST["PadImage"]."' width = '120'> </td>";
                echo "<td> ".$_POST["PadPrice"]." </td>";
                echo "<td> ".$_SESSION["nPad"]." </td>";
                echo "</tr>";
            }

            // handle Mac
            if($_SESSION["Mac"] == "Mac") {
                echo "<br> Calculate Total: Price = ".$_POST["MacPrice"]." * ".$_SESSION["nMac"];
                $total += $_POST["MacPrice"]*$_SESSION["nMac"];

                echo "<tr> <td> <img src = '".$_POST["MacImage"]."' width = '120'> </td>";
                echo "<td> ".$_POST["MacPrice"]." </td>";
                echo "<td> ".$_SESSION["nMac"]." </td>";
                echo "</tr>";
            }

            echo "</table>";

            echo "<hr>";
            echo "<h3>The total cost is $".$total." . </h3>";
            $_SESSION["total_cost"] = $total;
            echo "<h3> Please verify your order and <a href = 'action22-2.php'> proceed to checkout. </a> </h3>";
            echo "<h3> or <a href = 'Activity22.php'> continue shopping </a> </h3>";
        ?>
    </body>
</html>