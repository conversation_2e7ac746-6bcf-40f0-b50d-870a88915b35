<?php
    function test_input($data) {
            $data = trim($data);
            $data = stripslashes($data);
            $data = htmlspecialchars($data);
            return $data;
    }
?>

<?php
    function drawTrapezoid($top, $bottom, $symbol)
    {
        for ($row = $top -1; $row < $bottom; $row++) {
            for ($x = 0; $x < $row + 1; $x++) {
                echo $symbol;
            }
            echo "<br>";
        }
    }

    function showMessage() {
        echo "Hello World!";
        echo "<br>";
    }

    function showImage($weather) {
        echo "It is ".$weather."! <br>";
        if ($weather == "Freezing") {
            $image = "/img/freezing.jpg";
        } else if ($weather == "Cold") {
            $image = "/img/cold.jpg";
        } else if ($weather == "Hot") {
            $image = "/img/hot.jpg";
        } else {
            //warm picture
            $image = "/img/warm.jpg";
        }
        echo "<img src = '".$image."' width = '500px'>";
    }
?>