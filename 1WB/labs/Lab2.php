<html>
    <head>
        <title>Lab 2</title>

        <style> 
            h1, p {
                text-align: center;
            }
        </style>
    </head>

    <body>
        <h1>Lab 2 </h1>
        <p>Submitted by <PERSON> </p>
        
        <hr>

        <h2>Display a Multiplication Table of Random Size from 5*5 to 20*20</h2>
        
        <?php
            $size = rand(5, 20);
            echo "This is a multiplication table of {$size} * {$size}";
            echo "<br> <br>";

            echo "<table border = '1' style = 'width: 50%; margin: auto; '>";
            
            for ($i = 1; $i <= $size; $i++) {
                $rowColor = ($i % 2 == 0) ? 'pink' : 'brown';
                echo "<tr style='background-color: {$rowColor};'>";
                
                for ($j = 1; $j <= $size; $j++) {
                    echo "<td style='text-align: center; padding: 5px;'>" . ($i * $j) . "</td>";
                }
                echo "</tr>";
            }
            echo "</table>";
        ?>
    </body>
</html>