<html>
    <head>
        <title>Lab 5</title>
    </head>

    <body>
        <h1>Lab 5</h1>
        <p>Submitted by <PERSON></p>
        <hr>

        <h2>HTML/CSS Quiz</h2>

        <?php
            if ($_SERVER["REQUEST_METHOD"] == "POST") {
                $answer = array(
                    "Q1" => "B",
                    "Q2" => "B",
                    "Q3" => "B",
                    "Q4" => "A",
                    "Q5" => "B",
                    "Q6" => "C",
                    "Q7" => "A",
                    "Q8" => "B",
                    "Q9" => "C",
                    "Q10" => "A"
                );

                $score = 0;

                foreach ($answer as $question => $correctAnswer) {
                    if (isset($_POST[$question])) {
                        if ($_POST[$question] == $correctAnswer) {
                            $score++;
                        }
                    }
                }

                echo "<h3>Your Score: $score / 10</h3>";

                if ($score == 10) {
                    echo "<p>Excellent! You got all answers correct!</p>";
                } elseif ($score >= 7) {
                    echo "<p>Great job! You did well.</p>";
                } elseif ($score >= 5) {
                    echo "<p>Good effort! Keep practicing.</p>";
                } else {
                    echo "<p>Keep studying! You can improve.</p>";
                }
            }

            echo "<hr>";

        ?>

        <form method="post">
            <p>1. What does HTML stand for?</p>
            <input type="radio" name="Q1" value="A"> Home Tool Markup Language<br>
            <input type="radio" name="Q1" value="B"> Hyper Text Markup Language<br>
            <input type="radio" name="Q1" value="C"> Hyperlinks and Text Markup Language<br>

            <p>2. What is the correct HTML element for inserting a line break?</p>
            <input type="radio" name="Q2" value="A"> &lt;lb&gt;<br>
            <input type="radio" name="Q2" value="B"> &lt;br&gt;<br>
            <input type="radio" name="Q2" value="C"> &lt;break&gt;<br>

            <p>3. Which property is used to change the background color in CSS?</p>
            <input type="radio" name="Q3" value="A"> color<br>
            <input type="radio" name="Q3" value="B"> background-color<br>
            <input type="radio" name="Q3" value="C"> bgcolor<br>

            <p>4. Which HTML tag is used to define an internal style sheet?</p>
            <input type="radio" name="Q4" value="A"> &lt;style&gt;<br>
            <input type="radio" name="Q4" value="B"> &lt;css&gt;<br>
            <input type="radio" name="Q4" value="C"> &lt;script&gt;<br>

            <p>5. How do you make text bold in CSS?</p>
            <input type="radio" name="Q5" value="A"> font-weight: normal;<br>
            <input type="radio" name="Q5" value="B"> font-weight: bold;<br>
            <input type="radio" name="Q5" value="C"> text-style: bold;<br>

            <p>6. How do you create an ordered list in HTML?</p>
            <input type="radio" name="Q6" value="A"> &lt;ul&gt;<br>
            <input type="radio" name="Q6" value="B"> &lt;dl&gt;<br>
            <input type="radio" name="Q6" value="C"> &lt;ol&gt;<br>

            <p>7. Which CSS property controls the text size?</p>
            <input type="radio" name="Q7" value="A"> font-size<br>
            <input type="radio" name="Q7" value="B"> text-style<br>
            <input type="radio" name="Q7" value="C"> text-size<br>

            <p>8. How do you make a hyperlink open in a new tab?</p>
            <input type="radio" name="Q8" value="A"> &lt;a href="url" target="self"&gt;<br>
            <input type="radio" name="Q8" value="B"> &lt;a href="url" target="_blank"&gt;<br>
            <input type="radio" name="Q8" value="C"> &lt;a href="url" target="new"&gt;<br>

            <p>9. Which CSS property is used to control the space between elements?</p>
            <input type="radio" name="Q9" value="A"> padding<br>
            <input type="radio" name="Q9" value="B"> border-spacing<br>
            <input type="radio" name="Q9" value="C"> margin<br>

            <p>10. What is the correct way to apply an external CSS file?</p>
            <input type="radio" name="Q10" value="A"> &lt;link rel="stylesheet" type="text/css" href="style.css"&gt;<br>
            <input type="radio" name="Q10" value="B"> &lt;style src="style.css"&gt;<br>
            <input type="radio" name="Q10" value="C"> &lt;css href="style.css"&gt;<br>

            <br><br>
            <input type="submit" value="Submit">
        </form>
    </body>
</html>