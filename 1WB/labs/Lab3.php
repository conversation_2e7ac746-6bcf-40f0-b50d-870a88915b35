<html>
    <head>
        <title>Lab 3</title>
    </head>
    
    <body>
        <h1>Lab 3</h1>
        <p>Submitted by <PERSON></p>
        <hr>

        <h2>PHP Quiz</h2>

        <?php
            if ($_SERVER["REQUEST_METHOD"] == "POST") {
                $answer = array(
                    "Q1" => "B",
                    "Q2" => "A",
                    "Q3" => "B",
                    "Q4" => "B",
                    "Q5" => "A",
                    "Q6" => "B",
                    "Q7" => "B",
                    "Q8" => "B",
                    "Q9" => "A",
                    "Q10" => "B"
                );

                $score = 0;

                foreach ($answer as $question => $correctAnswer) {
                    if (isset($_POST[$question])) {
                        if ($_POST[$question] == $correctAnswer) {
                            $score++;
                        }
                    }
                }
                
                echo "<h3>Your Score: $score / 10</h3>";
                
                if ($score == 10) {
                    echo "<p>Excellent! You got all answers correct!</p>";
                } elseif ($score >= 7) {
                    echo "<p>Great job! You did well.</p>";
                } elseif ($score >= 5) {
                    echo "<p>Good effort! Keep practicing.</p>";
                } else {
                    echo "<p>Keep studying! You can improve.</p>";
                }
            }
            
        ?>
        
        <hr>

        <form method = "post">
            <p> 1. What does PHP stand for?</p>
            <input type = "radio" name = "Q1" value = "A"> Personal Hypertext Processor<br>
            <input type = "radio" name = "Q1" value = "B"> PHP: Hypertext Preprocessor<br>
            <input type = "radio" name = "Q1" value = "C"> Private Home Page<br>

            <p>2. What is the default file extension for PHP files?</p>
            <input type = "radio" name = "Q2" value = "A"> .php<br>
            <input type = "radio" name = "Q2" value = "B"> .html<br>
            <input type = "radio" name = "Q2" value = "C"> .xml<br>

            <p>3. Which PHP statement is used to output text?</p>
            <input type = "radio" name = "Q3" value = "A"> print<br>
            <input type = "radio" name = "Q3" value = "B"> echo<br>
            <input type = "radio" name = "Q3" value = "C"> write<br>

            <p>4. Which of the following is TRUE in PHP?</p>
            <input type = "radio" name = "Q4" value = "A"> PHP is case-insensitive<br>
            <input type = "radio" name = "Q4" value = "B"> Both variables and array keys are case-sensitive<br>
            <input type = "radio" name = "Q4" value = "C"> Variables are case-insensitive, but array keys are case-sensitive<br>

            <p>5. Can PHP run on different operating systems?</p>
            <input type="radio" name="Q5" value="A"> Yes<br>
            <input type="radio" name="Q5" value="B"> No<br>

            <p>6. What keyword is used to define a function in PHP?</p>
            <input type = "radio" name = "Q6" value = "A"> define<br>
            <input type = "radio" name = "Q6" value = "B"> function<br>
            <input type = "radio" name = "Q6" value = "C"> method<br>

            <p>7. Which PHP superglobal is used to collect form data sent with the POST method?</p>
            <input type = "radio" name = "Q7" value = "A"> $_GET<br>
            <input type = "radio" name = "Q7" value = "B"> $_POST<br>
            <input type = "radio" name = "Q7" value = "C"> $_REQUEST<br>

            <p>8. What does the dot (.) operator do in PHP?</p>
            <input type = "radio" name = "Q8" value = "A"> Multiplication operator<br>
            <input type = "radio" name = "Q8" value = "B"> Concatenation operator<br>
            <input type = "radio" name = "Q8" value = "C"> Assignment operator<br>

            <p>9. Which function is used to terminate script execution in PHP?</p>
            <input type = "radio" name = "Q9" value = "A"> exit()<br>
            <input type = "radio" name = "Q9" value = "B"> die()<br>
            <input type = "radio" name = "Q9" value = "C"> break()<br>

            <p>10. What is the default server name for running PHP on localhost?</p>
            <input type = "radio" name = "Q10" value = "A"> 127.0.0.1<br>
            <input type = "radio" name = "Q10" value = "B"> localhost<br>
            <input type = "radio" name = "Q10" value = "C"> myserver<br>

            <br><br>

            <input type="submit" value="Submit">
        </form>

    </body>
</html>