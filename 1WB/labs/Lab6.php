<html>
    <head>
        <title>Lab 6</title>
        
        <style>
            .error {
                color: red;
            }
            body {
                background-color: #e7e7e7;
                margin: 0;
                padding: 0;
                font-size: 16px;
            }
            h1 {
                text-align: center;
                margin-top: 40px;
                font-size: 40px;
                font-family: Arial, sans-serif;
            }
            .nav {
                font-size: 18px;
                font-weight: bold;
                font-family: Arial, sans-serif;
                text-align: center;
                overflow: hidden;
                padding: 10px 0;
            }
            .nav a {
                color: midnightblue;
                text-align: center;
                display: inline-block;
                padding: 14px 20px;
            }
            .container {
                background-color: white;
                padding: 10px;
                margin: 20px auto;
                width: 600px;
            }

            .button {
                background-color: lightgrey;
                color: black;
                border: none;
                border-radius: 5px;
                padding: 5px;
                width: 100px;
            }
            .button:hover {
                background-color: gray;
            }

        </style>
    </head>

    <?php
        $lastnameErr = $firstnameErr = $genderErr = $affiliationErr = $emailErr = $cellphoneErr = $passwordErr = "";
        $lastname = $firstname = $gender = $affiliation = $email = $cellphone = $cellphone2 = $wechat = $covid = $password1 = $password2 = "";

        if ($_SERVER["REQUEST_METHOD"] == "POST") {
            $lastname = test_input($_POST["lastname"]);
            $firstname = test_input($_POST["firstname"]);
            $gender = isset($_POST["gender"]) ? test_input($_POST["gender"]) : "";
            $affiliation = test_input($_POST["affiliation"]);
            $email = test_input($_POST["email"]);
            $cellphone = test_input($_POST["phone"]); // Corrected field name
            $cellphone2 = test_input($_POST["phone2"]); // Corrected field name
            $wechat = test_input($_POST["wechat"]);
            $covid = test_input($_POST["covid"]);
            $password1 = test_input($_POST["password1"]);
            $password2 = test_input($_POST["password2"]);

            //LAST NAME
            if ($lastname == "") {
                $lastnameErr = "Lastname is required!";
                $flag = 1;
            } else {
                if (!preg_match("/^[a-zA-Z ]*$/",$lastname)) {
                    $lastnameErr = "Only letters and white space allowed!";
                    $flag = 2;
                }
            }

            //FIRST NAME
            if ($firstname == "") {
                $firstnameErr = "Firstname is required!";
                $flag = 3;
            } else {
                if (!preg_match("/^[a-zA-Z ]*$/",$firstname)) {
                    $firstnameErr = "Only letters and white space allowed!";
                    $flag = 4;
                }
            }

            //GENDER
            if ($gender == "") {
                $genderErr = "Gender is required!";
                $flag = 5;
            }

            //AFFILIATION
            if ($affiliation == "") {
                $affiliationErr = "Affiliation is required";
                $flag = 6;
            }

            //EMAIL*
            if ($email == "") {
                $emailErr = "Email is required!";
                $flag = 7;
            }

            //PHONE*
            if ($cellphone == "") {
                $cellphoneErr = "Phone number is required!";
                $flag = 8;
            }

            //Password
            if ($password1 == "") {
                $passwordErr = "Password is required!";
                $flag = 9;
            } else {
                if ($password1 != $password2) {
                    $passwordErr = "Password does not match!";
                    $flag = 10;
                }
            }
        }

        function test_input($data) {
            $data = trim($data);
            $data = stripslashes($data);
            $data = htmlspecialchars($data);
            return $data;
        }
    ?>

    <body>
            <h1>APATH</h1>

            <div class = "nav">
                <a>Home</a>
                <a href = "/labs/Lab6.php">Personal Profile</a>
                <a>Car Info</a>
                <a>House Info</a>
                <a>Pickup Assignment</a>
                <a>Temp House Assignment</a>
                <a>Logout</a>
            </div>

            <div class = "container">
            <form action = "<?php echo htmlspecialchars($_SERVER["PHP_SELF"]);?>" method = "POST">
                Last Name:
                <span class = "error">* <?php echo $lastnameErr; ?> </span>
                <input type = "text" name = "lastname" value = "<?php echo $lastname; ?>">
                <br>

                First Name:
                <span class = "error">* <?php echo $firstnameErr; ?> </span>
                <input type = "text" name = "firstname" value = "<?php echo $firstname; ?>">
                <br>

                Gender:
                <span class = "error">* <?php echo $genderErr; ?> </span>
                <input type = "radio" name = "gender" value = "Female"  <?php if (isset($gender) && $gender == "Female") echo "checked"; ?> > Female
                <input type = "radio" name = "gender" value = "Male"    <?php if (isset($gender) && $gender == "Male") echo "checked"; ?>   > Male
                <input type = "radio" name = "gender" value = "Other"   <?php if (isset($gender) && $gender == "Other") echo "checked"; ?>  > Other
                <br>

                Affiliation/Recommended by:
                <span class = "error">* <?php echo $affiliationErr; ?> </span>
                <input type = "text" name = "affiliation" value = "<?php echo $affiliation; ?>">
                <br>

                Email:
                <span class = "error">* <?php echo $emailErr; ?> </span>
                <input type = "text" name = "email" value = "<?php echo $email; ?>">
                <br>

                Cell Phone to contact you:
                <span class = "error">* <?php echo $cellphoneErr; ?> </span>
                <input type = "text" name = "phone" value = "<?php echo $cellphone; ?>">
                <br>

                Backup Phone to contact you:
                <input type = "text" name = "phone2" value = "<?php echo $cellphone2; ?>">
                <br>

                WeChat:
                <input type = "text" name = "wechat" value = "<?php echo $wechat; ?>">
                <br>

                Did you already get COVID Vaccine (Yes or No):
                <input type = "text" name = "covid" value = "<?php echo $covid; ?>">
                <br>

                Password:
                <span class = "error">* <?php echo $passwordErr; ?> </span>
                <input type = "password" name = "password1" maxlength = "30">
                <br>
            
                Confirm Password:
                <span class = "error">* <?php echo $passwordErr; ?> </span>
                <input type = "password" name = "password2" maxlength = "30">
                <br>
                <br>

                <input type = "submit" class = "button">
            </form>
        </div>
    </body>
</html>