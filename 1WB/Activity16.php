<html>
    <head>
        <title>Activity 16</title>
    </head>

    <body>
        <h1>Activity 16 - February 28, 2025</h1>
        <p>Submitted by <PERSON> </p>
        <hr>

        <h1>Open and process file already in the Server</h1>

        <?php
            $file = "Students-Grades-Info.txt";
            $studentStr = file_get_contents($file);
            $studentStr = trim($studentStr);
            $studentList = explode("\n", $studentStr); // 1D array for each studentList
            $nFound = 0;

            foreach ($studentList as $index => $student) {
                $studentInfo[$index] = explode("\t", $student); // 2D array
            }

            echo "<h3>Progress Bar with Student Major</h3>";
            $allMajor = array("Digital Media", "Security", "Business", "Software", "Other");
            $allFound = array(0, 0, 0, 0, 0);

            foreach ($allMajor as $index => $major) {
                foreach ($studentInfo as $student) {
                    if ($major == $student[2])
                        $allFound[$index]++;

                }
            }

            // use progress bar to show summary of student major information
            echo "<table>";
            echo "<tr>";
                echo "<td>Major</td> <td>No. of Students</td> <td>Percentage</td> <td>Bar</td>";
            echo "</tr>";

            foreach ($allMajor as $index => $major) {
                $p = $allFound[$index] / count($studentInfo) * 100;
                $p = round($p, 2);

                echo "<tr>";
                    echo "<td> ".$major." </td>";
                    echo "<td> ".$allFound[$index]." </td>";
                    echo "<td> ".$p." </td>";
                    echo "<td> <progress max = '100' min = '0' value = ".$p."> </progress> </td>";
                echo "</tr>";
            }

            echo "</table>";

            /*
            //TABLE ONE
            echo "Display file/2D array as a table";
            echo "<table border = '1'>";
            echo "<tr>";
                echo "<td>No. </td>
                        <td>Name </td> 
                        <td>Email </td>
                        <td>Major </td>
                        <td>Grade </td>
                        <td>IP </td>";
            echo "</tr>";

            foreach ($studentInfo as $index => $student) {
                echo "<tr>";
                    echo "<td> ".($index + 1)." </td>"; //echo "<td> ".$index." </td>";
                    foreach ($student as $info) {
                        echo "<td> ".$info." </td>";
                    }
                echo "</tr>";
            }
            echo "</table>";
            */

            //TABLE TWO
            echo "<hr>";
            echo "<h2> Removing Duplicates IP Address and save the clean copy in a new file</h2>";

            $myfile1 = fopen("newfile.txt", "w") or die("Unable to open ths file!");

            echo "<table border = '1'>";
            echo "<tr>";
                echo "<td>No. </td>
                        <td>Name </td> 
                        <td>Email </td>
                        <td>Major </td>
                        <td>Grade </td>
                        <td>IP </td>";
            echo "</tr>";

            foreach ($studentInfo as $index => $student) {
                $ip = $student[4];
                $repeating = false;
                foreach ($studentInfo as $i => $person) {
                    if ($ip == $person[4] && $index > $i) {
                        $repeating = true;
                        break;
                    }
                    if ($index <= $i)
                        break;
                } // end of inner foreach
                if (!$repeating) {
                    $nFound++;
                    echo "<tr>";
                        echo "<td> ".$nFound." </td>";
                        fwrite($myfile1, $nFound);
                        fwrite($myfile1, ",");
                        foreach ($student as $ind => $info) {
                            echo "<td> ".$info." </td>";
                            fwrite($myfile1, $info);
                            if ($ind < 4)
                                fwrite($myfile1, ",");
                        }
                        echo "</tr>";
                        fwrite($myfile1, "\n");
                }
            } // end of outer foreach
            echo "</table>";

            //TABLE THREE
            echo "<hr>";
            echo "<h2> Removing Duplicate Names </h2>";

            echo "<h3> Create a clean copy without duplicate names. </h3>";
            echo "<h3> Display student information about grades. Display a progress bar with students in A, B, C, D, or lower. </h3>";

            $myfile2 = fopen("newfile2.txt", "w") or die("Unable to open this file!");

            echo "<table border = '1'>";
            echo "<tr>";
                echo "<td>No. </td>
                        <td>Name </td> 
                        <td>Email </td>
                        <td>Major </td>
                        <td>Grade </td>
                        <td>IP </td>";
            echo "</tr>";

            $uniqueNames = [];
            $nFound = 0;

            foreach ($studentInfo as $index => $student) {
                $name = $student[0];
                if (!in_array($name, $uniqueNames)) {
                    $uniqueNames[] = $name;
                    $nFound++;

                    echo "<tr>";
                        echo "<td> ".$nFound." </td>";
                        fwrite($myfile2, $nFound . ",");

                        foreach ($student as $ind => $info) {
                            echo "<td> " . $info . " </td>";
                            fwrite($myfile2, $info);
                            if ($ind < 4)
                                fwrite($myfile2, ",");
                    }
                    fwrite($myfile2, "\n");
                    echo "</tr>";
            }
        }
        echo "</table>";

        $student2Str = file_get_contents("newfile2.txt");
        $student2Str = trim($student2Str);
        $student2List = explode("\n", $student2Str);
        $student2Info = [];

        foreach ($student2List as $index => $student) {
            $student2Info[$index] = explode(",", $student);
        }

        echo "<hr>";
        echo "<h2> Progress Bar for Student Grades </h2>";

        $gradeCategories = array("A", "B", "C", "D", "F");
        $gradeCounts = array(0, 0, 0, 0, 0);

        foreach ($student2Info as $student) {
            $grade = trim($student[4]);
            if ($grade >= 90) {
                $gradeCounts[0]++;
            } elseif ($grade >= 80) {
                $gradeCounts[1]++;
            } elseif ($grade >= 70) {
                $gradeCounts[2]++;
            } elseif ($grade >= 60) {
                $gradeCounts[3]++;
            } else {
                $gradeCounts[4]++;
            }
        }

        echo "<table>";
        echo "<tr>";
            echo "<td>Grade</td> <td>No. of Students</td> <td>Percentage</td> <td>Progress Bar</td>";
        echo "</tr>";

        foreach ($gradeCategories as $index => $grade) {
            $percentage = ($gradeCounts[$index] / count($student2Info)) * 100;
            $percentage = round($percentage, 2);

            echo "<tr>";
                echo "<td>".$grade."</td>";
                echo "<td>".$gradeCounts[$index]."</td>";
                echo "<td>".$percentage."%</td>";
                echo "<td><progress max='100' min='0' value='".$percentage."'></progress></td>";
            echo "</tr>";
        }

        echo "</table>";

        ?>

    </body>
</html>