<html>
    <head>
        <title>Activity 9</title>
    </head>

    <body>
        <?php
            function test_input($data) {
                $data = trim($data);
                $data = stripslashes($data);
                $data = htmlspecialchars($data);
                return $data;
            }

            if ($_SERVER["REQUEST_METHOD"] == "POST") {
                $email = test_input($_POST["email"]);
                $pw = test_input($_POST["pw"]); // avoid using 'password' as the variable name

                include "connection.php";

                //query based on email
                $sqs = "SELECT * FROM users WHERE email = '$email'";
                $result = mysqli_query($dbc, $sqs);

                $numrows = mysqli_num_rows($result);
                if ($numrows == 1) {
                    $row = mysqli_fetch_array($result);
                    $dbpw = $row["pw"]; // "pw" is the name of the column in the database table
                    $dbfirstname = $row["firstname"];

                    if ($pw == $dbpw) {
                        echo "Welcome to out website, ".$dbfirstname."!<br>";
                        mysqli_close($dbc);
                        header("Location: user_home.php");
                    } else {
                        echo "Sorry, your password is not correct!";
                    }
                } else if ($numrows == 0) {
                    echo "Email is not in our system. Please register first!";
                } else {
                    echo "Something happened! Please try again later.";
                }
            }

    ?>
        <h1>Activity 9 - February 5, 2025</h1>
        <p>Submitted by Matilda Vazquez-Guzman </p>
        <hr>

        <h1>Welcome to Matilda's Free Online Testing Site</h1>
        <p>If you already have an account with us, please login.</p>
        <p>Otherwise, please <a href = "Activity8.php"> sign up.</a></p>

        <form action = "<?php echo htmlspecialchars($_SERVER["PHP_SELF"]);?>" method = "POST">
            Email: <input type = "text" name = "email" maxlength = "50">
            <br><br>

            Password: <input type = "password" name = "pw" maxlength = "25">
            <br><br>
            <input type = "submit" name = "Login" value = "LOGIN">
        </form>
    </body>
</html>

