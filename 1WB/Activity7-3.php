<html>
    <head>
        <title>Activity 7</title>
        
        <style>
            .error { color: #FF0000;}
        </style>
    </head>

    <body>
        <h1>Activity 7 - Jan 24, 2025; Updated Jan 27, 2025</h1>
        <p>Submitted by <PERSON> </p>
        
        <hr>

        <h2>Form Example 3: Using Post Method, Trigger SELF</h2>

        <?php
            $nameErr = $emailErr = $timeErr = $genderErr = $Q1Err = "";
            $name = "Firstname Lastname"; // default value
            $email = "<EMAIL>"; // default value
            $comment = $gender = $Q1 = $time = "";
            
            if ($_SERVER["REQUEST_METHOD"] == "POST") {
                $name = test_input($_POST["name"]);
                $email = test_input($_POST["email"]);
                $comment = test_input($_POST["comment"]);
                $gender = $_POST["gender"];
                $Q1 = $_POST["Q1"];
                $time = $_POST["time"];
                
                if ($name == "") {
                    $nameErr = "Name is required!";
                } elseif ($name == "Firstname Lastname") {
                    $nameErr = "Please enter a valid name.";
                } else {
                    if (!preg_match("/^[a-zA-Z ]*$/",$name)) {
                        $nameErr = "Only letters and white space allowed!";
                    }
                }

                if ($email == "") {
                    $emailErr = "Email is required!";
                } elseif ($email == "<EMAIL>") {
                    $emailErr = "Please enter a valid email.";
                } else {
                    if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
                        $emailErr = "Invalid email format";
                    }
                }

                if (empty($_POST["gender"])) {
                    $genderErr = "Gender is required!";
                } else {
                    $gender = $_POST["gender"];
                }
                
                if ($time == "----") {
                    $timeErr = "Time is required!";
                }

                if (empty($_POST["Q1"])) {
                    $Q1Err = "Please select an answer.";
                } else {
                    $Q1 = $_POST["Q1"];
                    if ($Q1 == "110") {
                        $Q1Feedback = "Good job!";
                    } else {
                        $Q1Feedback = "Sorry, that's not correct!";
                    }
                }
                
                echo "Your email is ". $email."<br>";
                echo "Your comment is ". $comment."<br>";
            }

            function test_input($data) {
                $data = trim($data);
                $data = stripslashes($data);
                $data = htmlspecialchars($data);
                return $data;
              }
        ?>
        
        <form action = "<?php echo htmlspecialchars($_SERVER["PHP_SELF"]);?>" method = "POST">
            Name:
            <input type = "text" name = "name" value = "<?php echo $name; ?>">
            <span class = "error">* <?php echo $nameErr; ?> </span>
            
            <br><br>

            Email:
            <input type = "text" name = "email" value = "<?php echo $email; ?>">
            <span class = "error">* <?php echo $emailErr; ?> </span>
            
            <br><br>

            Gender: <br>
            <input type = "radio" name = "gender" value = "Female"
            <?php if (isset($gender) && $gender == "Female") echo "checked"; ?>
            > Female
            
            <input type = "radio" name = "gender" value = "Male"
            <?php if (isset($gender) && $gender == "Male") echo "checked"; ?>
            > Male
            
            <input type = "radio" name = "gender" value = "Other"
            <?php if (isset($gender) && $gender == "Other") echo "checked"; ?>
            > Other
            
            <br><br>

            Quiz <br>
            1. What is the result of 45+65? <br>
            <input type = "radio" name = "Q1" value = "100"
            <?php if (isset($Q1) && $Q1 == "100") echo "checked"; ?>
            > 100

            <input type = "radio" name = "Q1" value = "110"
            <?php if (isset($Q1) && $Q1 == "110") echo "checked"; ?>
            > 110
            
            <input type = "radio" name = "Q1" value = "120"
            <?php if (isset($Q1) && $Q1 == "120") echo "checked"; ?>
            > 120

            <br><br>

            Select your preferred time:
            <span class = "error">* <?php echo $timeErr; ?> </span>

            <select name = "time">
                <option> ---- </option>
                <option value = "AM"
                <?php if ($time == "AM") echo "selected"; ?>
                > MWF 9am - 11am </option>
                
                <option value = "PM"
                <?php if ($time == "PM") echo "selected"; ?>
                > TuTh 2pm - 4pm </option>
                
                <option value = "EV"
                <?php if ($time == "EV") echo "selected"; ?>
                > M-F 6pm - 7pm </option>
            </select>

            <br><br>

            Comment:
            <textarea name = "comment" rows = "5" cols = "40"> <?php echo $comment; ?> </textarea>

            <br><br>

            <input type = "submit">
        </form>

        <hr>
        
        <h3>Testing Area: For Developer Only</h3>
        <?php
            echo "Data collected from the form: <br>";
            echo "Name: ".$name;
            echo "<br> Email: ".$email;
            echo "<br> Gender: ".$gender;
            echo "<br> Q1 = ".$Q1;
            echo "<br> Time: ".$time;
            echo "<br> Comment: ".$comment;
        ?>

    </body>
</html>