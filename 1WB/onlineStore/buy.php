<?php
session_start();
include 'config.php';
include 'user_nav.php';

// Check if user is logged in
if (!isset($_SESSION['id'])) {
    header("Location: login.php");
    exit();
}

$user_id = $_SESSION['id'];

// Process purchase if form submitted
if ($_SERVER["REQUEST_METHOD"] == "POST" && isset($_POST['buy_items'])) {
    // Process selected items
    // Update database to mark items as sold
    // Redirect to confirmation page
}

// Get available items (not owned by current user)
$query = "SELECT i.*, u.firstname, u.lastname FROM items i 
          JOIN users u ON i.seller_id = u.id 
          WHERE i.status = 'available' AND i.seller_id != $user_id 
          ORDER BY i.upload_date DESC";
$items = mysqli_query($dbc, $query);
?>

<!DOCTYPE html>
<html>
<head>
    <title>Buy Items - GGC Marketplace</title>
    <link rel="stylesheet" type="text/css" href="style.css">
</head>
<body>
<div class="container">
    <h1>I Want to Buy</h1>

    <form method="POST" action="">
        <div class="items-grid">
            <?php while ($item = mysqli_fetch_assoc($items)): ?>
                <div class="item-card">
                    <img src="uploads/<?php echo $item['image']; ?>" alt="<?php echo $item['title']; ?>">
                    <h3><?php echo $item['title']; ?></h3>
                    <p class="price">$<?php echo $item['price']; ?></p>
                    <p class="seller">Seller: <?php echo $item['firstname'] . ' ' . $item['lastname']; ?></p>
                    <p class="description"><?php echo $item['description']; ?></p>
                    <input type="checkbox" name="items[]" value="<?php echo $item['id']; ?>">
                    <label>Add to cart</label>
                </div>
            <?php endwhile; ?>
        </div>

        <button type="submit" name="buy_items">Purchase Selected Items</button>
    </form>
</div>
</body>
</html>
