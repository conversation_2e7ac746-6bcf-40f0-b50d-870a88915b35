<?php
session_start();
include 'config.php';
include 'admin_nav.php';

// Check if admin is logged in
if (!isset($_SESSION['id']) || $_SESSION['type'] != 0) {
    header("Location: login.php");
    exit();
}

// Get admin info
$admin_id = $_SESSION['id'];
$query = "SELECT * FROM users WHERE id = $admin_id";
$result = mysqli_query($dbc, $query);
$admin = mysqli_fetch_assoc($result);

// Get counts for dashboard
$query_users = "SELECT COUNT(*) as count FROM users WHERE type = 1";
$result_users = mysqli_query($dbc, $query_users);
$user_count = mysqli_fetch_assoc($result_users)['count'];

$query_items = "SELECT COUNT(*) as count FROM items WHERE status = 'available'";
$result_items = mysqli_query($dbc, $query_items);
$item_count = mysqli_fetch_assoc($result_items)['count'];

$query_sold = "SELECT COUNT(*) as count FROM items WHERE status = 'sold'";
$result_sold = mysqli_query($dbc, $query_sold);
$sold_count = mysqli_fetch_assoc($result_sold)['count'];

// Get recent announcements
$query_announcements = "SELECT * FROM announcements ORDER BY created_date DESC LIMIT 5";
$announcements = mysqli_query($dbc, $query_announcements);
?>

<!DOCTYPE html>
<html>
<head>
    <title>Admin Dashboard - GGC Marketplace</title>
    <link rel="stylesheet" type="text/css" href="style.css">
</head>
<body>
    <div class="container">
        <h1>Admin Dashboard</h1>
        
        <div class="admin-dashboard">
            <div class="dashboard-card">
                <h3>Users</h3>
                <p class="count"><?php echo $user_count; ?></p>
                <a href="admin_users.php">Manage Users</a>
            </div>
            
            <div class="dashboard-card">
                <h3>Items For Sale</h3>
                <p class="count"><?php echo $item_count; ?></p>
                <a href="admin_items.php">Manage Items</a>
            </div>
            
            <div class="dashboard-card">
                <h3>Items Sold</h3>
                <p class="count"><?php echo $sold_count; ?></p>
                <a href="admin_sold.php">View Sold Items</a>
            </div>
            
            <div class="dashboard-card">
                <h3>Announcements</h3>
                <a href="admin_announcements.php">Manage Announcements</a>
            </div>
        </div>
        
        <h2>Recent Announcements</h2>
        <?php if (mysqli_num_rows($announcements) > 0): ?>
            <div class="announcements-list">
                <?php while ($announcement = mysqli_fetch_assoc($announcements)): ?>
                    <div class="announcement">
                        <h3><?php echo $announcement['title']; ?></h3>
                        <p class="date"><?php echo date('M d, Y', strtotime($announcement['created_date'])); ?></p>
                        <p><?php echo $announcement['content']; ?></p>
                    </div>
                <?php endwhile; ?>
            </div>
        <?php else: ?>
            <p>No announcements yet.</p>
        <?php endif; ?>
    </div>
</body>
</html>