<?php
session_start();
include 'config.php';
include 'nav.php';

$emailErr = $pwErr = "";
$email = $pw = "";

if (isset($_GET['registered']) && $_GET['registered'] == 'true') {
    echo "<div class='success-message'>Registration successful! Please login.</div>";
}

if ($_SERVER["REQUEST_METHOD"] == "POST") {
    if (empty($_POST["email"])) {
        $emailErr = "Email is required";
    } else {
        $email = mysqli_real_escape_string($dbc, test_input($_POST["email"]));
    }

    if (empty($_POST["pw"])) {
        $pwErr = "Password is required";
    } else {
        $pw = test_input($_POST["pw"]);
    }

    if (empty($emailErr) && empty($pwErr)) {
        $query = "SELECT * FROM users WHERE email='$email'";
        $result = mysqli_query($dbc, $query);

        if (mysqli_num_rows($result) == 1) {
            $user = mysqli_fetch_assoc($result);

            if ($pw === $user['pw']) {
                $_SESSION['id'] = $user['id'];
                $_SESSION['type'] = $user['type'];

                switch ($user['type']) {
                    case 0:
                        header("Location: admin_home.php");
                        break;
                    case 1:
                        header("Location: user_home.php");
                        break;
                    default:
                        echo "Invalid user type!";
                        break;
                }
                exit();
            } else {
                $pwErr = "Invalid password!";
            }
        } else {
            $emailErr = "User not found!";
        }
    }
}
?>

<!DOCTYPE html>
<html>
<head>
    <title>Login - GGC Online Store</title>
    <link rel="stylesheet" type="text/css" href="style.css">
</head>
<body>
<div class="container">
    <h1>Login to GGC Online Store</h1>

    <form method="POST" action="<?php echo htmlspecialchars($_SERVER["PHP_SELF"]);?>">
        <div class="form-group">
            <label>Email:</label>
            <input type="email" name="email" value="<?php echo $email; ?>">
            <span class="error"><?php echo $emailErr; ?></span>
        </div>

        <div class="form-group">
            <label>Password:</label>
            <input type="password" name="pw">
            <span class="error"><?php echo $pwErr; ?></span>
        </div>

        <div class="checkbox-container">
            <input type="checkbox" name="remember"> Remember me
            <a href="forgot_password.php">Forgot password?</a>
        </div>

        <button type="submit">Login</button>
    </form>

    <p>No Account? <a href="register.php">Create One</a></p>
</div>
</body>
</html>