<?php
session_start();
include 'config.php';
include 'nav.php';

// Get available items
$query = "SELECT i.*, u.firstname, u.lastname FROM items i 
          JOIN users u ON i.seller_id = u.id 
          WHERE i.status = 'available' 
          ORDER BY i.upload_date DESC";
$items = mysqli_query($dbc, $query);
?>

<!DOCTYPE html>
<html>
<head>
    <title>GGC Marketplace - Browse Items</title>
    <link rel="stylesheet" type="text/css" href="style.css">
</head>
<body>
    <div class="container">
        <h1>Welcome to GGC Marketplace</h1>
        <p>This website provides GGC students a platform to exchange used items such as computers, books, small furniture, electronics, etc.</p>
        <p>Browse items below or <a href="login.php">login</a> to buy and sell.</p>

        <div class="items-grid">
            <?php while ($item = mysqli_fetch_assoc($items)): ?>
                <div class="item-card">
                    <img src="uploads/<?php echo $item['image']; ?>" alt="<?php echo $item['title']; ?>">
                    <h3><?php echo $item['title']; ?></h3>
                    <p class="price">$<?php echo $item['price']; ?></p>
                    <p class="seller">Seller: <?php echo $item['firstname'] . ' ' . $item['lastname']; ?></p>
                    <p class="description"><?php echo $item['description']; ?></p>
                    <a href="login.php" class="buy-button">Login to Buy</a>
                </div>
            <?php endwhile; ?>
        </div>
    </div>
</body>
</html>