<?php
session_start();
include 'config.php';
include 'user_nav.php';

// Check if user is logged in
if (!isset($_SESSION['id']) || $_SESSION['type'] != 1) {
    header("Location: login.php");
    exit();
}

// Get user info
$user_id = $_SESSION['id'];
$query = "SELECT * FROM users WHERE id = $user_id";
$result = mysqli_query($dbc, $query);
$user = mysqli_fetch_assoc($result);

// Get announcements
$query = "SELECT * FROM announcements ORDER BY created_date DESC LIMIT 5";
$announcements = mysqli_query($dbc, $query);
?>

<!DOCTYPE html>
<html>
<head>
    <title>Home - GGC Marketplace</title>
    <link rel="stylesheet" type="text/css" href="style.css">
</head>
<body>
<div class="container">
    <h1>Welcome to GGC Marketplace, <?php echo $user['firstname']; ?>!</h1>

    <div class="how-to-use">
        <h2>How to Use GGC Marketplace</h2>
        <p>This platform allows GGC students to buy and sell used items like computers, books, furniture, and electronics.</p>
        <ul>
            <li>To sell items, go to the "I Want to Sell" page and list up to 5 items.</li>
            <li>To buy items, browse the "I Want to Buy" page and add items to your cart.</li>
            <li>View your transaction history to see what you've bought and sold.</li>
            <li>Update your profile information as needed.</li>
        </ul>
    </div>
</div>
</body>
</html>