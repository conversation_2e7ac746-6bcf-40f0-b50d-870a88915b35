<?php
session_start();
include 'config.php';
include 'user_nav.php';

// Check if user is logged in
if (!isset($_SESSION['id']) || $_SESSION['type'] != 1) {
    header("Location: login.php");
    exit();
}

$user_id = $_SESSION['id'];
$titleErr = $descriptionErr = $priceErr = $imageErr = "";
$title = $description = $price = "";
$success = false;

// Count user's current listings
$query = "SELECT COUNT(*) as count FROM items WHERE seller_id = $user_id AND status = 'available'";
$result = mysqli_query($dbc, $query);
$row = mysqli_fetch_assoc($result);
$item_count = $row['count'];

if ($_SERVER["REQUEST_METHOD"] == "POST" && $item_count < 5) {
    // Process form submission for new item
    // Handle file upload for image
    // Insert into database if valid
}

// Get user's current listings
$query = "SELECT * FROM items WHERE seller_id = $user_id ORDER BY upload_date DESC";
$user_items = mysqli_query($dbc, $query);
?>

<!DOCTYPE html>
<html>
<head>
    <title>Sell Items - GGC Marketplace</title>
    <link rel="stylesheet" type="text/css" href="style.css">
</head>
<body>
<div class="container">
    <h1>I Want to Sell</h1>

    <?php if ($item_count >= 5): ?>
        <div class="alert">You have reached the maximum of 5 items for sale.</div>
    <?php else: ?>
        <!-- Form to add new item -->
    <?php endif; ?>

    <h2>Your Current Listings</h2>
    <div class="items-grid">
        <?php while ($item = mysqli_fetch_assoc($user_items)): ?>
            <!-- Display each item -->
        <?php endwhile; ?>
    </div>
</div>
</body>
</html>