<?php
session_start();
include 'config.php';
include 'user_nav.php';

// Check if user is logged in
if (!isset($_SESSION['id'])) {
    header("Location: login.php");
    exit();
}

$user_id = $_SESSION['id'];
$phoneErr = $emailErr = $pwErr = $pw2Err = $imageErr = "";
$success = false;

// Get user info
$query = "SELECT * FROM users WHERE id = $user_id";
$result = mysqli_query($dbc, $query);
$user = mysqli_fetch_assoc($result);

if ($_SERVER["REQUEST_METHOD"] == "POST") {
    $phone = test_input($_POST["phone"]);
    $email = test_input($_POST["email"]);
    $current_pw = test_input($_POST["current_pw"]);
    $new_pw = isset($_POST["new_pw"]) ? test_input($_POST["new_pw"]) : "";
    $confirm_pw = isset($_POST["confirm_pw"]) ? test_input($_POST["confirm_pw"]) : "";
    
    // Validate email
    if ($email != $user['email']) {
        $check_email = "SELECT * FROM users WHERE email='$email' AND id != $user_id";
        $email_result = mysqli_query($dbc, $check_email);
        if (mysqli_num_rows($email_result) > 0) {
            $emailErr = "Email already in use";
        }
    }
    
    // Validate password change
    if (!empty($new_pw)) {
        if ($current_pw != $user['pw']) {
            $pwErr = "Current password is incorrect";
        } elseif ($new_pw != $confirm_pw) {
            $pw2Err = "New passwords do not match";
        }
    }
    
    // Handle profile image upload
    $profile_image = $user['profile_image'];
    if (isset($_FILES['profile_image']) && $_FILES['profile_image']['size'] > 0) {
        $target_dir = "uploads/profiles/";
        $file_extension = pathinfo($_FILES["profile_image"]["name"], PATHINFO_EXTENSION);
        $new_filename = "user_" . $user_id . "_" . time() . "." . $file_extension;
        $target_file = $target_dir . $new_filename;
        
        // Check file type
        $allowed_types = array('jpg', 'jpeg', 'png', 'gif');
        if (!in_array(strtolower($file_extension), $allowed_types)) {
            $imageErr = "Only JPG, JPEG, PNG & GIF files are allowed.";
        } elseif ($_FILES["profile_image"]["size"] > 5000000) { // 5MB max
            $imageErr = "File is too large. Max 5MB.";
        } elseif (move_uploaded_file($_FILES["profile_image"]["tmp_name"], $target_file)) {
            $profile_image = $new_filename;
        } else {
            $imageErr = "Error uploading file.";
        }
    }
    
    // Update user if no errors
    if (empty($emailErr) && empty($pwErr) && empty($pw2Err) && empty($imageErr)) {
        $update_query = "UPDATE users SET email='$email', phone='$phone', profile_image='$profile_image'";
        
        if (!empty($new_pw)) {
            $update_query .= ", pw='$new_pw'";
        }
        
        $update_query .= " WHERE id=$user_id";
        
        if (mysqli_query($dbc, $update_query)) {
            $success = true;
        } else {
            echo "Error updating profile: " . mysqli_error($dbc);
        }
    }
}

// Get updated user info
$query = "SELECT * FROM users WHERE id = $user_id";
$result = mysqli_query($dbc, $query);
$user = mysqli_fetch_assoc($result);
?>

<!DOCTYPE html>
<html>
<head>
    <title>My Profile - GGC Marketplace</title>
    <link rel="stylesheet" type="text/css" href="style.css">
</head>
<body>
    <div class="container">
        <h1>My Profile</h1>
        
        <?php if ($success): ?>
            <div class="success-message">Profile updated successfully!</div>
        <?php endif; ?>
        
        <form method="POST" action="<?php echo htmlspecialchars($_SERVER["PHP_SELF"]);?>" enctype="multipart/form-data">
            <div class="profile-section">
                <div class="profile-image">
                    <?php if (!empty($user['profile_image'])): ?>
                        <img src="uploads/profiles/<?php echo $user['profile_image']; ?>" alt="Profile Image">
                    <?php else: ?>
                        <div class="no-image">No Image</div>
                    <?php endif; ?>
                    
                    <input type="file" name="profile_image">
                    <span class="error"><?php echo $imageErr; ?></span>
                </div>
                
                <div class="profile-details">
                    <div class="form-group">
                        <label>First Name:</label>
                        <input type="text" value="<?php echo $user['firstname']; ?>" disabled>
                        <span class="note">Cannot be changed</span>
                    </div>
                    
                    <div class="form-group">
                        <label>Last Name:</label>
                        <input type="text" value="<?php echo $user['lastname']; ?>" disabled>
                        <span class="note">Cannot be changed</span>
                    </div>
                    
                    <div class="form-group">
                        <label>Email:</label>
                        <input type="email" name="email" value="<?php echo $user['email']; ?>">
                        <span class="error"><?php echo $emailErr; ?></span>
                    </div>
                    
                    <div class="form-group">
                        <label>Phone:</label>
                        <input type="text" name="phone" value="<?php echo $user['phone']; ?>">
                        <span class="error"><?php echo $phoneErr; ?></span>
                    </div>
                </div>
            </div>
            
            <h2>Change Password</h2>
            <div class="form-group">
                <label>Current Password:</label>
                <input type="password" name="current_pw">
                <span class="error"><?php echo $pwErr; ?></span>
            </div>
            
            <div class="form-group">
                <label>New Password:</label>
                <input type="password" name="new_pw">
            </div>
            
            <div class="form-group">
                <label>Confirm New Password:</label>
                <input type="password" name="confirm_pw">
                <span class="error"><?php echo $pw2Err; ?></span>
            </div>
            
            <button type="submit">Update Profile</button>
        </form>
    </div>
</body>
</html>