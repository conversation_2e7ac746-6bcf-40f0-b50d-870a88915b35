<?php
session_start();
include 'config.php';
include 'nav.php';

$emailErr = $answerErr = $pwErr = $pw2Err = "";
$email = $answer = $pw = $pw2 = "";
$showQuestion = false;
$showPasswordReset = false;
$securityQuestion = "";

if ($_SERVER["REQUEST_METHOD"] == "POST") {
    // Step 1: Find user by email
    if (isset($_POST['find_email']) && !$showQuestion && !$showPasswordReset) {
        if (empty($_POST["email"])) {
            $emailErr = "Email is required";
        } else {
            $email = mysqli_real_escape_string($dbc, test_input($_POST["email"]));
            $query = "SELECT * FROM users WHERE email='$email'";
            $result = mysqli_query($dbc, $query);

            if (mysqli_num_rows($result) == 1) {
                $user = mysqli_fetch_assoc($result);
                $_SESSION['reset_user_id'] = $user['id'];
                $securityQuestion = $user['security_question'];
                $showQuestion = true;
            } else {
                $emailErr = "Email not found";
            }
        }
    }

    // Step 2: Verify security answer
    if (isset($_POST['verify_answer']) && $showQuestion) {
        if (empty($_POST["answer"])) {
            $answerErr = "Answer is required";
        } else {
            $answer = test_input($_POST["answer"]);
            $user_id = $_SESSION['reset_user_id'];

            $query = "SELECT * FROM users WHERE id='$user_id'";
            $result = mysqli_query($dbc, $query);
            $user = mysqli_fetch_assoc($result);

            if ($answer === $user['security_answer']) {
                $showPasswordReset = true;
                $showQuestion = false;
            } else {
                $answerErr = "Incorrect answer";
            }
        }
    }

    // Step 3: Reset password
    if (isset($_POST['reset_password']) && $showPasswordReset) {
        if (empty($_POST["pw"])) {
            $pwErr = "New password is required";
        } else {
            $pw = test_input($_POST["pw"]);
        }

        if (empty($_POST["pw2"])) {
            $pw2Err = "Please confirm password";
        } else {
            $pw2 = test_input($_POST["pw2"]);
            if ($pw != $pw2) {
                $pw2Err = "Passwords do not match";
            }
        }

        if (empty($pwErr) && empty($pw2Err)) {
            $user_id = $_SESSION['reset_user_id'];
            $query = "UPDATE users SET pw='$pw' WHERE id='$user_id'";

            if (mysqli_query($dbc, $query)) {
                unset($_SESSION['reset_user_id']);
                header("Location: login.php?reset=true");
                exit();
            } else {
                echo "Error updating password: " . mysqli_error($dbc);
            }
        }
    }
}
?>

<!DOCTYPE html>
<html>
<head>
    <title>Forgot Password - GGC Online Store</title>
    <link rel="stylesheet" type="text/css" href="style.css">
</head>
<body>
<div class="container">
    <h1>Reset Your Password</h1>

    <?php if (!$showQuestion && !$showPasswordReset): ?>
        <!-- Step 1: Enter email -->
        <form method="POST" action="<?php echo htmlspecialchars($_SERVER["PHP_SELF"]);?>">
            <div class="form-group">
                <label>Enter your email:</label>
                <input type="email" name="email" value="<?php echo $email; ?>">
                <span class="error"><?php echo $emailErr; ?></span>
            </div>

            <button type="submit" name="find_email">Continue</button>
        </form>

    <?php elseif ($showQuestion): ?>
        <!-- Step 2: Answer security question -->
        <form method="POST" action="<?php echo htmlspecialchars($_SERVER["PHP_SELF"]);?>">
            <div class="form-group">
                <label>Security Question: <?php echo $securityQuestion; ?></label>
                <input type="text" name="answer" value="<?php echo $answer; ?>">
                <span class="error"><?php echo $answerErr; ?></span>
            </div>

            <button type="submit" name="verify_answer">Verify</button>
        </form>

    <?php elseif ($showPasswordReset): ?>
        <!-- Step 3: Reset password -->
        <form method="POST" action="<?php echo htmlspecialchars($_SERVER["PHP_SELF"]);?>">
            <div class="form-group">
                <label>New Password:</label>
                <input type="password" name="pw">
                <span class="error"><?php echo $pwErr; ?></span>
            </div>

            <div class="form-group">
                <label>Confirm New Password:</label>
                <input type="password" name="pw2">
                <span class="error"><?php echo $pw2Err; ?></span>
            </div>

            <button type="submit" name="reset_password">Reset Password</button>
        </form>
    <?php endif; ?>

    <p><a href="login.php">Back to Login</a></p>
</div>
</body>
</html>