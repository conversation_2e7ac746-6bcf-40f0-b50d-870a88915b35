<?php
    session_start();
    include 'config.php';
    include 'nav.php';

    $firstnameErr = $lastnameErr = $emailErr = $phoneErr = $pwErr = $pw2Err = $questionErr = $answerErr = "";
    $firstname = $lastname = $email = $phone = $pw = $pw2 = $question = $answer = "";
    $registered = false;

    if ($_SERVER["REQUEST_METHOD"] == "POST") {
        if (empty($_POST["firstname"])) {
            $firstnameErr = "First name is required";
        } else {
            $firstname = test_input($_POST["firstname"]);
        }

        if (empty($_POST["lastname"])) {
            $lastnameErr = "Last name is required";
        } else {
            $lastname = test_input($_POST["lastname"]);
        }

        if (empty($_POST["email"])) {
            $emailErr = "Email is required";
        } else {
            $email = test_input($_POST["email"]);
            $query = "SELECT * FROM users WHERE email='$email'";
            $result = mysqli_query($dbc, $query);

            if (mysqli_num_rows($result) > 0) {
                $emailErr = "Email already in use";
            }
        }

        if (empty($_POST["phone"])) {
            $phoneErr = "Phone number is required";
        } else {
            $phone = test_input($_POST["phone"]);
        }

        if (empty($_POST["pw"])) {
            $pwErr = "Password is required";
        } else {
            $pw = test_input($_POST["pw"]);
        }

        if (empty($_POST["pw2"])) {
            $pw2Err = "Please confirm password";
        } else {
            $pw2 = test_input($_POST["pw2"]);
            if ($pw != $pw2) {
                $pw2Err = "Passwords do not match";
            }
        }

        if (empty($_POST["question"])) {
            $questionErr = "Security question is required";
        } else {
            $question = test_input($_POST["question"]);
        }

        if (empty($_POST["answer"])) {
            $answerErr = "Security answer is required";
        } else {
            $answer = test_input($_POST["answer"]);
        }

        // If no errors, insert into database
        if (empty($firstnameErr) && empty($lastnameErr) && empty($emailErr) && empty($phoneErr)
            && empty($pwErr) && empty($pw2Err) && empty($questionErr) && empty($answerErr)) {

            $query = "INSERT INTO users (firstname, lastname, email, phone, pw, security_question, security_answer) 
                  VALUES ('$firstname', '$lastname', '$email', '$phone', '$pw', '$question', '$answer')";

            if (mysqli_query($dbc, $query)) {
                $registered = true;
                header("Location: login.php?registered=true");
                exit();
            } else {
                echo "Error: " . mysqli_error($dbc);
            }
        }
    }
?>

<!DOCTYPE html>
<html>
    <head>
        <title>Register - GGC Online Store</title>
        <link rel="stylesheet" type="text/css" href="style.css">
    </head>

    <body>
        <div class="container">
            <h1>Create an Account</h1>

            <form method="POST" action="<?php echo htmlspecialchars($_SERVER["PHP_SELF"]);?>">
                <div class="form-group">
                    <label>First Name:</label>
                    <input type="text" name="firstname" value="<?php echo $firstname; ?>">
                    <span class="error"><?php echo $firstnameErr; ?></span>
                </div>

                <div class="form-group">
                    <label>Last Name:</label>
                    <input type="text" name="lastname" value="<?php echo $lastname; ?>">
                        <span class="error"><?php echo $lastnameErr; ?></span>
                </div>

                <div class="form-group">
                    <label>Email:</label>
                    <input type="email" name="email" value="<?php echo $email; ?>">
                    <span class="error"><?php echo $emailErr; ?></span>
                </div>

                <div class="form-group">
                    <label>Phone:</label>
                    <input type="text" name="phone" value="<?php echo $phone; ?>">
                    <span class="error"><?php echo $phoneErr; ?></span>
                </div>

                <div class="form-group">
                    <label>Password:</label>
                    <input type="password" name="pw">
                    <span class="error"><?php echo $pwErr; ?></span>
                </div>

                <div class="form-group">
                    <label>Confirm Password:</label>
                    <input type="password" name="pw2">
                    <span class="error"><?php echo $pw2Err; ?></span>
                </div>

                <div class="form-group">
                    <label>Security Question:</label>
                    <select name="question">
                        <option value="">Select a question</option>
                        <option value="What was your first pet's name?">What was your first pet's name?</option>
                        <option value="What is your mother's maiden name?">What is your mother's maiden name?</option>
                        <option value="What high school did you attend?">What high school did you attend?</option>
                        <option value="What was your childhood nickname?">What was your childhood nickname?</option>
                    </select>
                    <span class="error"><?php echo $questionErr; ?></span>
                </div>

                <div class="form-group">
                    <label>Answer:</label>
                    <input type="text" name="answer" value="<?php echo $answer; ?>">
                    <span class="error"><?php echo $answerErr; ?></span>
                </div>

                <button type="submit">Register</button>
            </form>

            <p>Already have an account? <a href="login.php">Login here</a></p>
        </div>
    </body>
</html>