<?php
session_start();
include 'config.php';
include 'user_nav.php';

// Check if user is logged in
if (!isset($_SESSION['id'])) {
    header("Location: login.php");
    exit();
}

$user_id = $_SESSION['id'];

// Get items sold by user
$query_sold = "SELECT i.*, b.firstname as buyer_firstname, b.lastname as buyer_lastname, i.sold_date 
               FROM items i 
               JOIN users b ON i.buyer_id = b.id 
               WHERE i.seller_id = $user_id AND i.status = 'sold' 
               ORDER BY i.sold_date DESC";
$sold_items = mysqli_query($dbc, $query_sold);

// Get items bought by user
$query_bought = "SELECT i.*, s.firstname as seller_firstname, s.lastname as seller_lastname, i.sold_date 
                 FROM items i 
                 JOIN users s ON i.seller_id = s.id 
                 WHERE i.buyer_id = $user_id 
                 ORDER BY i.sold_date DESC";
$bought_items = mysqli_query($dbc, $query_bought);
?>

<!DOCTYPE html>
<html>
<head>
    <title>Transaction History - GGC Marketplace</title>
    <link rel="stylesheet" type="text/css" href="style.css">
</head>
<body>
    <div class="container">
        <h1>Transaction History</h1>
        
        <h2>Items You've Sold</h2>
        <?php if (mysqli_num_rows($sold_items) > 0): ?>
            <div class="items-grid">
                <?php while ($item = mysqli_fetch_assoc($sold_items)): ?>
                    <div class="item-card">
                        <img src="uploads/<?php echo $item['image']; ?>" alt="<?php echo $item['title']; ?>">
                        <h3><?php echo $item['title']; ?></h3>
                        <p class="price">$<?php echo $item['price']; ?></p>
                        <p class="buyer">Buyer: <?php echo $item['buyer_firstname'] . ' ' . $item['buyer_lastname']; ?></p>
                        <p class="date">Sold on: <?php echo date('M d, Y', strtotime($item['sold_date'])); ?></p>
                    </div>
                <?php endwhile; ?>
            </div>
        <?php else: ?>
            <p>You haven't sold any items yet.</p>
        <?php endif; ?>
        
        <h2>Items You've Purchased</h2>
        <?php if (mysqli_num_rows($bought_items) > 0): ?>
            <div class="items-grid">
                <?php while ($item = mysqli_fetch_assoc($bought_items)): ?>
                    <div class="item-card">
                        <img src="uploads/<?php echo $item['image']; ?>" alt="<?php echo $item['title']; ?>">
                        <h3><?php echo $item['title']; ?></h3>
                        <p class="price">$<?php echo $item['price']; ?></p>
                        <p class="seller">Seller: <?php echo $item['seller_firstname'] . ' ' . $item['seller_lastname']; ?></p>
                        <p class="date">Purchased on: <?php echo date('M d, Y', strtotime($item['sold_date'])); ?></p>
                    </div>
                <?php endwhile; ?>
            </div>
        <?php else: ?>
            <p>You haven't purchased any items yet.</p>
        <?php endif; ?>
    </div>
</body>
</html>