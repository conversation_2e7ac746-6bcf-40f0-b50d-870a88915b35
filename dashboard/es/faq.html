<!doctype html>
<html lang="es">
  <head>
    <meta charset="utf-8">
    <!-- Always force latest IE rendering engine or request Chrome Frame -->
    <meta content="IE=edge,chrome=1" http-equiv="X-UA-Compatible">
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />

    <!-- Use title if it's in the page YAML frontmatter -->
    <title>XAMPP FAQs for Mac OS X</title>

    <meta name="description" content="Instructions on how to install XAMPP for OSX distributions." />
    

    <link href="/dashboard/stylesheets/normalize.css" rel="stylesheet" type="text/css" /><link href="/dashboard/stylesheets/all.css" rel="stylesheet" type="text/css" />
    <link href="//cdnjs.cloudflare.com/ajax/libs/font-awesome/3.1.0/css/font-awesome.min.css" rel="stylesheet" type="text/css" />

    <script src="/dashboard/javascripts/modernizr.js" type="text/javascript"></script>


    <link href="/dashboard/images/favicon.png" rel="icon" type="image/png" />


  </head>

  <body class="es es_faq">
    <div id="fb-root"></div>
    <script>(function(d, s, id) {
      var js, fjs = d.getElementsByTagName(s)[0];
      if (d.getElementById(id)) return;
      js = d.createElement(s); js.id = id;
      js.src = "//connect.facebook.net/en_US/all.js#xfbml=1&appId=277385395761685";
      fjs.parentNode.insertBefore(js, fjs);
    }(document, 'script', 'facebook-jssdk'));</script>
    <header class="header contain-to-grid">
      <nav class="top-bar" data-topbar>
        <ul class="title-area">
          <li class="name">
            <h1><a href="/dashboard/es/index.html">Apache Friends</a></h1>
          </li>
          <li class="toggle-topbar menu-icon">
            <a href="#">
              <span>Menu</span>
            </a>
          </li>
        </ul>

        <section class="top-bar-section">
          <!-- Left Nav Section -->
          <ul class="left">
              <li class="item active"><a href="/dashboard/es/faq.html">Preguntas frecuentes</a></li>
              <li class="item "><a href="/dashboard/es/howto.html">HOW-TO Guides</a></li>
              <li class="item "><a target="_blank" href="/dashboard/phpinfo.php">PHPInfo</a></li>
              <li class="item "><a href="/phpmyadmin/">phpMyAdmin</a></li>
          </ul>
        </section>
      </nav>
    </header>

    <div class="wrapper">
      <div class="hero">
  <div class="row">
    <div class="large-12 columns">
      <h1>OS X <span>Preguntas frecuentes</span></h1>
    </div>
  </div>
</div>
<div class="row">
    <div class="large-8 columns">
    <dl class="accordion">
    
      <dt>What is the difference between XAMPP for OS X and XAMPP-VM?</dt>
      <dd>
        <p>
        <ul>
          <li>XAMPP for OS X is a native installer for OS X. It installs Apache, PHP and other XAMPP components directly on your OS X system, in the /Applications/XAMPP folder.</li>
          <li>XAMPP-VM is a virtual machine for OS X. It includes Apache, PHP and other XAMPP components and runs them in a Linux-based virtual machine on your OS X system.</li>
        </ul>
        </p>
        <p>For more information, refer to the blog post at <a href="https://www.apachefriends.org/blog/new_xampp_20170628.html">https://www.apachefriends.org/blog/new_xampp_20170628.html</a>.</p>.
      </dd> 
    
      <dt>¿Cómo instalo XAMPP para Mac OS X?</dt>
      <dd>
      <p>Para instalar XAMPP haz lo siguiente:</p>
      <ul>
        <li>Abre la imagen DMG.</li>
        <li>Haz doble click en la imagen para iniciar el proceso de instalación.</li></ul>
      <p>Eso es todo. XAMPP está ahora instalado en el directorio /Applications/XAMPP.</p>
      </dd>
      <dt>Does XAMPP include MySQL or MariaDB?</dt>
      <dd>
        <p>Since XAMPP 5.5.30 and 5.6.14, XAMPP ships MariaDB instead of MySQL. The commands and tools are the same for both.</p>
      </dd>
      <dt>¿Cómo inicio XAMPP?</dt>
      <dd>
      <p>Para iniciar XAMPP, simplemente abre el Panel de Control de XAMPP e inicia Apache, MySQL y ProFTPD. El nombre del Panel de Control de XAMPP es "manager-osx".</p>      
      </dd>
      <dt>¿Cómo paro XAMPP?</dt>
      <dd>
      <p>Para parar XAMPP simplemente abre el Panel de Control de XAMPP y para los servidores. El nombre del Panel de Control de XAMPP es "manager-osx".</p>      
      </dd>
      <dt>¿Cómo compruebo que todo ha ido bien?</dt>
      <dd>
      <p>Escribe la siguiente URL en tu navegador:</p>
      <p><code>http://localhost</code></p>

      <p>You should see the XAMPP start page, as shown below.</p>
        <img src="/dashboard/images/screenshots/xampp-macosx-start.jpg" />    
      </dd>
      <dt>¿Está XAMPP listo para producción?</dt>
      <dd><p>XAMPP no está pensado para uso en producción, sino para entornos de desarrollo. XAMPP está configurado de forma que sea tan abierto como sea posible, permitiendo al desarrollador hacer lo que quiera. En entornos de desarrollo esto es magnífico pero en un entorno de producción puede ser fatal.</p>
      <p>A continuación se muestra una lista de posibles fallos de seguridad en XAMPP:</p>
      <ol>
        <li>El administrador de MySQL (root) no tiene password.</li>
        <li>El proceso MySQL está accesible a través de la red.</li>
        <li>ProFTPD usa la contraseña "lampp" para el usuario "daemon".</li>
      </ol>
      <p>Para arreglar la mayoría de las debilidades de seguridad, simplemente ejecuta el siguiente comando:</p>
      <p><code>sudo /Applications/XAMPP/xamppfiles/xampp security</code></p>
      <p>Esto iniciará una pequeña comprobación de seguridad para hacer la instalación de XAMPP segura.</p></dd>

      <dt>¿Cuál es el significado de los mensajes de error que veo cuando inicio XAMPP?</dt>
      <dd>
        <p>Puedes obtener muchos mensajes de error mientras inicias XAMPP:</p>
        <p><code>LAMPP-Apache is already running.<br />
            An Apache daemon is already running.</code></p>
        <p>El script de inicio de LAMPP no inició XAMPP-Apache porque ya hay otro servidor Apache iniciado. Para iniciar XAMPP correctamente, primero tienes que parar ese servidor.</p>
        <p><code>LAMPP-MySQL is already running.<br />
            A MySQL daemon is already running.</code></p>
        <p>Este error es similar al anterior. El script de inicio de LAMPP encontró un servidor de MySQL iniciado en tu sistema. Para iniciar LAMPP correctamente, tienes que parar ese servidor primero.</p>
      </dd>

      <dt>Apache doesn't seem to start. What can I do?</dt>
      <dd>
        <p>Este error puede aparecer por múltiples razones. Apache lo muestra bajo muchas circunstancias. Para encontrar la razón exacta, tenemos que investigar:</p>
        <p><code>tail -2 /Applications/XAMPP/logs/error_log</code></p>
        <p>Si recibes algún mensaje de error visita <a href="/community.html">nuestra comunidad</a> para obtener ayuda</p>
      </dd>

      <dt>¿Cómo puedo hacer mi instalación de XAMPP más segura?</dt>
      <dd>
        <p>En la instalación por defecto, XAMPP no tiene establecida contraseña y no es recomendable ejecutar XAMPP con esta configuración accesible por otros.</p>
        <p>Simplemente ejecuta el siguiente comando (como root) para iniciar una pequeña comprobación de seguridad:</p>
        <p><code>sudo /Applications/XAMPP/xamppfiles/xampp security</code></p>
        <p>Ahora deberías ver el siguiente cuadro de diálogo en tu pantalla:</p>
        <p><code>
XAMPP: Quick security check...</br>
XAMPP: MySQL is accessable via network.</br>
XAMPP: Normaly that's not recommended. Do you want me to turn it off? [yes] yes</br>
XAMPP: Turned off.</br>
XAMPP: Stopping MySQL...</br>
XAMPP: Starting MySQL...</br>
XAMPP: The MySQL/phpMyAdmin user pma has no password set!!!</br>
XAMPP: Do you want to set a password? [yes] yes</br>
XAMPP: Password: ******</br>
XAMPP: Password (again): ******</br>
XAMPP: Setting new MySQL pma password.</br>
XAMPP: Setting phpMyAdmin's pma password to the new one.</br>
XAMPP: MySQL has no root passwort set!!!</br>
XAMPP: Do you want to set a password? [yes] yes</br>
XAMPP: Write the passworde somewhere down to make sure you won't forget it!!!</br>
XAMPP: Password: ******</br>
XAMPP: Password (again): ******</br>
XAMPP: Setting new MySQL root password.</br>
XAMPP: Setting phpMyAdmin's root password to the new one.</br>
XAMPP: The FTP password for user 'nobody' is still set to 'lampp'.</br>
XAMPP: Do you want to change the password? [yes] yes</br>
XAMPP: Password: ******</br>
XAMPP: Password (again): ******</br>
XAMPP: Reload ProFTPD...</br>
XAMPP: Done.</br>
  </code></p>
        <p>(1) Establecer una contraseña protegerá las páginas de ejemplo de XAMPP (http://localhost/xampp/) usando esta contraseña. ¡El nombre de usuario es 'lampp'!</p>
        <p>Tras ejecutar este comando tu instalación de XAMPP debería ser más segura.</p>
      </dd>

      <dt>¿Cómo activo la extensión OCI8/Oracle para PHP?</dt>
      <dd>
        <p>Para activar la extensión OCI8/Oracle para PHP ejecuta el siguiente comando:</p>
        <p><code>sudo /Applications/XAMPP/xamppfiles/lampp oci8</code></p>
        <p>El siguiente diálogo se iniciará:</p>
        <p><code>Please enter the path to your Oracle or Instant Client installation:</br>
[/Applications/XAMPP/xamppfiles/lib/instantclient-********.0] </br>
installing symlinks...</br>
patching php.ini...</br>
OCI8 add-on activation likely successful.</br>
LAMPP: Stopping Apache with SSL...</br>
LAMPP: Starting Apache with SSL...</code></p>
        <p>Ahora la extensión debería estar activada.</p>
      </dd>

      <dt>¿Cómo habilitar el accesso a phpMyAdmin desde fuera?</dt>
      <dd>
        <p>En la configuración básica de XAMPP, phpMyAdmin es accessible sólo desde la máquina donde está instalada en http://127.0.0.1 o http://localhost.</p>
        <p>IMPORTANTE: Habilitar el accesso de phpMyAdmin para usuarios externos no está recomendado para entornos de producción. Un atacante podría usar cualquier vulnerabilidad de la aplicación para ejecutar código o modificar datos.</p>
        <p>Para habilitar el acceso remote a phpMyAdmin, sigue estos pasos:</p>
        <ul>
          <li>Edita el fichero /Applications/XAMPP/xamppfiles/etc/extra/httpd-xampp.conf en tu directorio de instalación.</li>
          <li>En este fichero encuentra las siguientes líneas. 
            <p><code>
                Alias /phpmyadmin "/Applications/XAMPP/xamppfiles/phpmyadmin"
                &lt;Directory "/Applications/XAMPP/xamppfiles/phpmyadmin"&gt;
                  AllowOverride AuthConfig
                  Require local
            </code></p>
          </li>
          <li>Reemplaza 'Require local' por 'Require all granted'.</li>
            <p><code>
                Alias /phpmyadmin "/Applications/XAMPP/xamppfiles/phpmyadmin"
                &lt;Directory "/Applications/XAMPP/xamppfiles/phpmyadmin"&gt;
                  AllowOverride AuthConfig
                  Require all granted
            </code></p>
          <li>Reinicia el servidor de XAMPP usando el panel de control.</li>
        </ul>
      </dd>

      <dt>Where are the main XAMPP configuration files?</dt>
      <dd>
        <p>The main XAMPP configuration files are located as follows:</p>
        <ul>
          <li>Apache configuration file: /Applications/XAMPP/xamppfiles/etc/httpd.conf, /Applications/XAMPP/xamppfiles/etc/extra/httpd-xampp.conf</li>
          <li>PHP configuration file: /Applications/XAMPP/xamppfiles/etc/php.ini</li>
          <li>MySQL configuration file: /Applications/XAMPP/xamppfiles/etc/my.cnf</li>
          <li>ProFTPD configuration file: /Applications/XAMPP/xamppfiles/etc/proftpd.conf</li>
        </ul>
      </dd>

      <dt>How do I send email with XAMPP?</dt>
      <dd>
        <p>To send email with XAMPP, use the PEAR Mail and Net_SMTP packages, which allow you to send email using an external SMTP account (such as a Gmail account). Follow these steps:</p>
        <ul>
          <li>Install the Mail and Net_SMTP PEAR modules:
          <code>
          pear install Net_SMTP Mail
          </code>
          Note that if these packages are already installed in your system you see the messages below when executing that command:
          <code>
          Ignoring installed package pear/Net_SMTP
          Ignoring installed package pear/Mail
          Nothing to install
          </code>
          </li>
          <li>
          Create the following example script in your "htdocs" directory to send an email:
          <code>
          &lt;?php
          require_once "Mail.php";

          $from = "<EMAIL>";
          $to = '<EMAIL>';

          $host = "ssl://smtp.gmail.com";
          $port = "465";
          $username = '<EMAIL>';
          $password = 'your-gmail-password';

          $subject = "test";
          $body = "test";

          $headers = array ('From' => $from, 'To' => $to,'Subject' => $subject);
          $smtp = Mail::factory('smtp',
             array ('host' => $host,
               'port' => $port,
               'auth' => true,
               'username' => $username,
               'password' => $password));

          $mail = $smtp->send($to, $headers, $body);

          if (PEAR::isError($mail)) {
            echo($mail->getMessage());
          } else {
            echo("Message successfully sent!\n");
          }
          ?>
          </code>
          <p>Remember to replace the dummy values shown with your actual Gmail address and account password. If you don't plan to use Gmail's SMTP server, replace the SMTP host details with appropriate values for your organization or ISP's SMTP server.</p>
          </li>
          <li>
          Execute the script by browsing to it using your Web browser. You should see a notification that the message was successfully sent, and the message should be delivered to the recipient email address.
          </li>
        </ul>
      </dd>
      
      <dt>¿Cómo hago una copia de seguridad/restauro mi sistema XAMPP?</dt>
      <dd>
        <p><strong>Aviso:</strong> La funcionalidad de copia de seguridad y restauración está bajo desarrollo y puede no funcionar correctamente.</p>
        <p>Puedes crear la copia de seguridad ejecutando:</p>
        <p><code>sudo /Applications/XAMPP/xamppfiles/xampp backup</code></p>
        <p>o</p>
        <p><code>sudo /Applications/XAMPP/xamppfiles/xampp backup secret</code></p>
        <p>Donde "secret" is tu contraseña de root de MySQL. Este comando generará la siguiente salida:</p>
        <p><code>Backing up databases...</br>
Backing up configuration, log and htdocs files...</br>
Calculating checksums...</br>
Building final backup file...</br>
Backup finished.</br>
Take care of /Applications/XAMPP/xamppfiles/backup/xampp-backup-22-01-14.sh</code></p>

        <p>El fichero /Applications/XAMPP/xamppfiles/backup/xampp-backup-22-01-14.sh (en el ejemplo de arriba) contiene tu copia de seguridad. Guardala en un lugar seguro.</p>

        <p>En la nueva máquina necesitas la misma versión de XAMPP que en tu máquina origen.</p>
        <p><code>sudo sh xampp-backup-22-01-14.sh</code></p>
        <p>Deberías ver algo así:</p>
        <p><code>Checking integrity of files...</br>
Restoring configuration, log and htdocs files...</br>
Checking versions...</br>
Installed: XAMPP 1.4.2</br>
Backup from: XAMPP 1.4.2</br>
Restoring MySQL databases...</br>
Restoring MySQL user databases...</br>
Backup complete. Have fun!</br>
You may need to restart XAMPP to complete the restore.</br>
  </code></p>
        <p>Eso es todo. Ten en cuenta que es una característica beta.</p>
      </dd>
    </dl>

  </div>
</div>

    </div>

    <footer class="footer row">
      <div class="columns">
        <div class="footer_lists-container row collapse">
          <div class="footer_social columns large-2">
            <ul class="social">
  <li class="twitter"><a href="https://twitter.com/apachefriends">Follow us on Twitter</a></li>
  <li class="facebook"><a href="https://www.facebook.com/we.are.xampp">Like us on Facebook</a></li>
</ul>

            <p class="footer_copyright">Copyright (c) 2022, Apache Friends</p>
          </div>
          <ul class="footer_links columns large-9">
            <li><a href="https://www.apachefriends.org/blog.html">Blog</a></li>
            <li><a href="/privacy_policy.html">Política de privacidad</a></li>
            <li>
<a target="_blank" href="http://www.fastly.com/">                CDN proporcionado por
                <img width="48" data-2x="/dashboard/images/<EMAIL>" src="/dashboard/images/fastly-logo.png" />
</a>            </li>
          </ul>
        </div>
      </div>
    </footer>

    <!-- JS Libraries -->
    <script src="//code.jquery.com/jquery-1.10.2.min.js"></script>
    <script src="/dashboard/javascripts/all.js" type="text/javascript"></script>
  </body>
</html>
