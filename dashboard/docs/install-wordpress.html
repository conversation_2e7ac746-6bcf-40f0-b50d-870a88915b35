<!doctype html>
<html lang="en">
  <head>
    <meta charset="utf-8">
    <!-- Always force latest IE rendering engine or request Chrome Frame -->
    <meta content="IE=edge,chrome=1" http-equiv="X-UA-Compatible">
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />

    <!-- Use title if it's in the page YAML frontmatter -->
    <title>Get Started Quickly with WordPress</title>

    
    

    <link href="/dashboard/stylesheets/normalize.css" rel="stylesheet" type="text/css" /><link href="/dashboard/stylesheets/all.css" rel="stylesheet" type="text/css" />
    <link href="//cdnjs.cloudflare.com/ajax/libs/font-awesome/3.1.0/css/font-awesome.min.css" rel="stylesheet" type="text/css" />

    <script src="/dashboard/javascripts/modernizr.js" type="text/javascript"></script>


    <link href="/dashboard/images/favicon.png" rel="icon" type="image/png" />


  </head>

  <body class="docs docs_install-wordpress">
    <div id="fb-root"></div>
    <script>(function(d, s, id) {
      var js, fjs = d.getElementsByTagName(s)[0];
      if (d.getElementById(id)) return;
      js = d.createElement(s); js.id = id;
      js.src = "//connect.facebook.net/en_US/all.js#xfbml=1&appId=277385395761685";
      fjs.parentNode.insertBefore(js, fjs);
    }(document, 'script', 'facebook-jssdk'));</script>
    <header class="header contain-to-grid">
      <nav class="top-bar" data-topbar>
        <ul class="title-area">
          <li class="name">
            <h1><a href="/dashboard/index.html">Apache Friends</a></h1>
          </li>
          <li class="toggle-topbar menu-icon">
            <a href="#">
              <span>Menu</span>
            </a>
          </li>
        </ul>

        <section class="top-bar-section">
          <!-- Left Nav Section -->
          <ul class="left">
              <li class="item "><a href="/dashboard/faq.html">FAQs</a></li>
              <li class="item active"><a href="/dashboard/howto.html">HOW-TO Guides</a></li>
              <li class="item "><a target="_blank" href="/dashboard/phpinfo.php">PHPInfo</a></li>
              <li class="item "><a href="/phpmyadmin/">phpMyAdmin</a></li>
          </ul>
        </section>
      </nav>
    </header>

    <div class="wrapper">
      <div class="hero">
  <div class="row">
    <div class="large-12 columns">
      <h1>Documentation</h1>
    </div>
  </div>
</div>
<div class="row">
  <div class="large-12 columns">
    <ul class="sub-nav">
      <li>
<a class="pdf" target="_blank" href="/dashboard/docs/install-wordpress.pdf">          Download PDF
          <span>install-wordpress.pdf</span>
</a>      </li>
    </ul>
    <article class="asciidoctor">
        <aside>
          <h3>Contents</h3>
          <ol class="sections">
              <li><a href="/dashboard/docs/install-wordpress.html#introduction">Introduction</a></li>
              <li><a href="/dashboard/docs/install-wordpress.html#assumptions_and_prerequisites">Assumptions and Prerequisites</a></li>
              <li><a href="/dashboard/docs/install-wordpress.html#step_1_install_bitnami_wordpress_module_for_xampp">Step 1: Install Bitnami WordPress module for XAMPP</a></li>
              <li><a href="/dashboard/docs/install-wordpress.html#step_2_test_wordpress">Step 2: Test WordPress</a></li>
              <li><a href="/dashboard/docs/install-wordpress.html#step_3_create_an_editor_account_and_start_blogging">Step 3: Create an Editor Account and Start Blogging</a></li>
              <li><a href="/dashboard/docs/install-wordpress.html#learn_more">Learn More</a></li>
          </ol>
        </aside>
      <h1>Get Started Quickly with WordPress</h1>
<div class="sect1">
<h2 id="_introduction">Introduction</h2>
<div class="sectionbody">
<div class="paragraph">
<p>Unresolved directive in &lt;stdin&gt; - include::/app/source-xampp-osx/docs/_includes/common/install-wordpress/introduction.adoc[]</p>
</div>
</div>
</div>
<div class="sect1">
<h2 id="_assumptions_and_prerequisites">Assumptions and Prerequisites</h2>
<div class="sectionbody">
<div class="paragraph">
<p>This tutorial doesn&#8217;t make a lot of assumptions, but the few that it does are important.</p>
</div>
<div class="ulist">
<ul>
<li>
<p>First, it assumes that you have a working XAMPP installation on Mac OS X, and that your XAMPP installation (including MySQL/MariaDB) is currently running. In case you don&#8217;t have this, <a href="https://www.apachefriends.org/download.html">download and install XAMPP</a> and then, once it&#8217;s installed, check that it&#8217;s all working by browsing to <a href="http://localhost" class="bare">http://localhost</a>. You should see something like this:</p>
<div class="imageblock">
<div class="content">
<img src="./images/install-wordpress/image01.png" alt="image01">
</div>
</div>
<div class="paragraph">
<p>You can also check that both MySQL/MariaDB and Apache are running using the XAMPP control panel:</p>
</div>
<div class="imageblock">
<div class="content">
<img src="./images/install-wordpress/image02.png" alt="image02">
</div>
</div>
</li>
<li>
<p>Second, it assumes that you&#8217;ve already downloaded the Mac OS X WordPress add-on for XAMPP. In case you don&#8217;t have this, you can <a href="https://bitnami.com/stack/xampp#wordpress">download it from the XAMPP Add-ons page</a>. The add-on is an executable that lets you click your way through key tasks such as configuring the WordPress administrator account and setting up WordPress email notifications.</p>
</li>
</ul>
</div>
<div class="paragraph">
<p>Did you check both the boxes above? You’re good to begin!</p>
</div>
</div>
</div>
<div class="sect1">
<h2 id="_step_1_install_bitnami_wordpress_module_for_xampp">Step 1: Install Bitnami WordPress module for XAMPP</h2>
<div class="sectionbody">
<div class="paragraph">
<p>You can now begin installing WordPress on top of XAMPP by following these steps:</p>
</div>
<div class="ulist">
<ul>
<li>
<p>Change to the directory containing the downloaded add-on and double-click the .dmg file to begin the installation process. This will launch the installer and you should see a splash screen, followed by a prompt for installation language. The installer is available in nine languages, including English, Spanish, Simplified Chinese, Korean, German and Russian. Select your language and you&#8217;ll be transferred to the main setup wizard.</p>
<div class="imageblock">
<div class="content">
<img src="./images/install-wordpress/image12.png" alt="image12">
</div>
</div>
</li>
<li>
<p>The WordPress add-on requires a pre-existing XAMPP installation. Select your XAMPP installation directory (usually, <em>/Applications/XAMPP</em>).</p>
<div class="imageblock">
<div class="content">
<img src="./images/install-wordpress/image13.png" alt="image13">
</div>
</div>
</li>
<li>
<p>Next, you&#8217;ll be prompted for the administrator password. Once you enter it, the installer will be restarted with administrator privileges.</p>
<div class="imageblock">
<div class="content">
<img src="./images/install-wordpress/image14.png" alt="image14">
</div>
</div>
</li>
<li>
<p>Repeat the previous steps and this time, you&#8217;ll be prompted to set up the WordPress administrator account. Enter a user name, real name and email address for the WordPress administrator account. Also, enter a password (minimum 6 characters) for the WordPress administrator account.</p>
<div class="imageblock">
<div class="content">
<img src="./images/install-wordpress/image15.png" alt="image15">
</div>
</div>
</li>
<li>
<p>Next, enter a title for your WordPress blog. Don&#8217;t worry, you can change this later!</p>
<div class="imageblock">
<div class="content">
<img src="./images/install-wordpress/image16.png" alt="image16">
</div>
</div>
</li>
<li>
<p>WordPress can optionally send you notifications on events, such as when someone comments on a post. The setup wizard lets you configure how these email notifications are sent out. You can either use a Gmail account or a custom mail server.</p>
<div class="imageblock">
<div class="content">
<img src="./images/install-wordpress/image17.png" alt="image17">
</div>
</div>
<div class="ulist">
<ul>
<li>
<p>If you&#8217;re using a Gmail account, simply enter your complete Gmail address and account password. For security reasons, it is recommended that you set up a separate Gmail account for notifications, rather than using your regular Gmail address.</p>
</li>
</ul>
</div>
<div class="imageblock">
<div class="content">
<img src="./images/install-wordpress/image18.png" alt="image18">
</div>
</div>
<div class="ulist">
<ul>
<li>
<p>If you don&#8217;t have a Gmail account or if you&#8217;d prefer to use a custom mail server, enter details for the mail server, such as the account username and password, SMTP server name and port, and security configuration.</p>
</li>
</ul>
</div>
<div class="imageblock">
<div class="content">
<img src="./images/install-wordpress/image19.png" alt="image19">
</div>
</div>
</li>
</ul>
</div>
<div class="paragraph">
<p>You&#8217;re almost done! Click Next a few times, decide whether you want to read more about Bitnami in a new browser window while the installation progresses, and the wizard will take care of the rest.</p>
</div>
<div class="imageblock">
<div class="content">
<img src="./images/install-wordpress/image110.png" alt="image110">
</div>
</div>
<div class="paragraph">
<p>Once installation is complete, you&#8217;ll see a success screen. Click "Finish" to exit the installation and launch WordPress.</p>
</div>
<div class="imageblock">
<div class="content">
<img src="./images/install-wordpress/image111.png" alt="image111">
</div>
</div>
</div>
</div>
<div class="sect1">
<h2 id="_step_2_test_wordpress">Step 2: Test WordPress</h2>
<div class="sectionbody">
<div class="paragraph">
<p>Unresolved directive in &lt;stdin&gt; - include::/app/source-xampp-osx/docs/_includes/common/install-wordpress/step2.adoc[]</p>
</div>
</div>
</div>
<div class="sect1">
<h2 id="_step_3_create_an_editor_account_and_start_blogging">Step 3: Create an Editor Account and Start Blogging</h2>
<div class="sectionbody">
<div class="paragraph">
<p>Unresolved directive in &lt;stdin&gt; - include::/app/source-xampp-osx/docs/_includes/common/install-wordpress/step3.adoc[]</p>
</div>
</div>
</div>
<div class="sect1">
<h2 id="_learn_more">Learn More</h2>
<div class="sectionbody">
<div class="paragraph">
<p>Unresolved directive in &lt;stdin&gt; - include::/app/source-xampp-osx/docs/_includes/common/install-wordpress/resources.adoc[]</p>
</div>
</div>
</div>
    </article>
  </div>
</div>

    </div>

    <footer class="footer row">
      <div class="columns">
        <div class="footer_lists-container row collapse">
          <div class="footer_social columns large-2">
            <ul class="social">
  <li class="twitter"><a href="https://twitter.com/apachefriends">Follow us on Twitter</a></li>
  <li class="facebook"><a href="https://www.facebook.com/we.are.xampp">Like us on Facebook</a></li>
</ul>

            <p class="footer_copyright">Copyright (c) 2022, Apache Friends</p>
          </div>
          <ul class="footer_links columns large-9">
            <li><a href="https://www.apachefriends.org/blog.html">Blog</a></li>
            <li><a href="/privacy_policy.html">Privacy Policy</a></li>
            <li>
<a target="_blank" href="http://www.fastly.com/">                CDN provided by
                <img width="48" data-2x="/dashboard/images/<EMAIL>" src="/dashboard/images/fastly-logo.png" />
</a>            </li>
          </ul>
        </div>
      </div>
    </footer>

    <!-- JS Libraries -->
    <script src="//code.jquery.com/jquery-1.10.2.min.js"></script>
    <script src="/dashboard/javascripts/all.js" type="text/javascript"></script>
  </body>
</html>
