<!doctype html>
<html lang="en">
  <head>
    <meta charset="utf-8">
    <!-- Always force latest IE rendering engine or request Chrome Frame -->
    <meta content="IE=edge,chrome=1" http-equiv="X-UA-Compatible">
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />

    <!-- Use title if it's in the page YAML frontmatter -->
    <title>Deploy an Application Using Git</title>

    
    

    <link href="/dashboard/stylesheets/normalize.css" rel="stylesheet" type="text/css" /><link href="/dashboard/stylesheets/all.css" rel="stylesheet" type="text/css" />
    <link href="//cdnjs.cloudflare.com/ajax/libs/font-awesome/3.1.0/css/font-awesome.min.css" rel="stylesheet" type="text/css" />

    <script src="/dashboard/javascripts/modernizr.js" type="text/javascript"></script>


    <link href="/dashboard/images/favicon.png" rel="icon" type="image/png" />


  </head>

  <body class="docs docs_deploy-git-app">
    <div id="fb-root"></div>
    <script>(function(d, s, id) {
      var js, fjs = d.getElementsByTagName(s)[0];
      if (d.getElementById(id)) return;
      js = d.createElement(s); js.id = id;
      js.src = "//connect.facebook.net/en_US/all.js#xfbml=1&appId=277385395761685";
      fjs.parentNode.insertBefore(js, fjs);
    }(document, 'script', 'facebook-jssdk'));</script>
    <header class="header contain-to-grid">
      <nav class="top-bar" data-topbar>
        <ul class="title-area">
          <li class="name">
            <h1><a href="/dashboard/index.html">Apache Friends</a></h1>
          </li>
          <li class="toggle-topbar menu-icon">
            <a href="#">
              <span>Menu</span>
            </a>
          </li>
        </ul>

        <section class="top-bar-section">
          <!-- Left Nav Section -->
          <ul class="left">
              <li class="item "><a href="/dashboard/faq.html">FAQs</a></li>
              <li class="item active"><a href="/dashboard/howto.html">HOW-TO Guides</a></li>
              <li class="item "><a target="_blank" href="/dashboard/phpinfo.php">PHPInfo</a></li>
              <li class="item "><a href="/phpmyadmin/">phpMyAdmin</a></li>
          </ul>
        </section>
      </nav>
    </header>

    <div class="wrapper">
      <div class="hero">
  <div class="row">
    <div class="large-12 columns">
      <h1>Documentation</h1>
    </div>
  </div>
</div>
<div class="row">
  <div class="large-12 columns">
    <ul class="sub-nav">
      <li>
<a class="pdf" target="_blank" href="/dashboard/docs/deploy-git-app.pdf">          Download PDF
          <span>deploy-git-app.pdf</span>
</a>      </li>
    </ul>
    <article class="asciidoctor">
      <h1>Deploy an Application Using Git</h1>
<div class="paragraph">
<p><a href="http://git-scm.com/">Git</a> is a popular distributed version control system. As you work with XAMPP, you might often find yourself needing to download and install applications or code libraries from a developer&#8217;s public git repository.</p>
</div>
<div class="paragraph">
<p>This guide walks you through the process, showing you how to use a git client to transfer code from a public online git repository to your XAMPP server. As an example, this guide assumes that you wish to download the latest version of <a href="http://www.slimframework.com/">Slim</a>, a popular PHP micro-framework for Web application development that also has its own Github repository.
NOTE: This guide uses the command-line git client for Mac OS X. If you don&#8217;t already have this, you can install it easily by running the command <em>brew install git</em> from a terminal.</p>
</div>
<div class="paragraph">
<p>To download files with git, you must first ensure that you have privileges to write to the <em>htdocs/</em> subdirectory of the XAMPP installation directory (typically, <em>/Applications/XAMPP</em>). Follow these steps:</p>
</div>
<div class="olist arabic">
<ol class="arabic">
<li>
<p>Open a new terminal and ensure you are logged in as an administrator.</p>
</li>
<li>
<p>Create a new group named <em>gitusers</em>. This group will contain those user accounts allowed to download files via git.</p>
<div class="imageblock">
<div class="content">
<img src="./images/deploy-git-app/image1.png" alt="image1">
</div>
</div>
</li>
<li>
<p>Add your account (in this example, <em>susan</em>) to the new group. Add other users if needed.</p>
<div class="imageblock">
<div class="content">
<img src="./images/deploy-git-app/image2.png" alt="image2">
</div>
</div>
</li>
<li>
<p>Change the ownership and permissions of the <em>htdocs/</em> subdirectory so that it is writable by the the new <em>gitusers</em> group.</p>
<div class="literalblock">
<div class="content">
<pre>cd /Applications/XAMPP/xamppfiles</pre>
</div>
</div>
<div class="literalblock">
<div class="content">
<pre>chown root:gitusers htdocs</pre>
</div>
</div>
<div class="literalblock">
<div class="content">
<pre>chmod 775 htdocs</pre>
</div>
</div>
<div class="imageblock">
<div class="content">
<img src="./images/deploy-git-app/image3.png" alt="image3">
</div>
</div>
<div class="admonitionblock tip">
<table>
<tr>
<td class="icon">
<i class="fa icon-tip" title="Tip"></i>
</td>
<td class="content">
If you&#8217;re using XAMPP in a single-user scenario and there will only be one user downloading files via git, you can skip creating a new group and instead simply transfer full ownership of the <em>htdocs/</em> directory to that user. To do this, use a command like <em>chown susan:susan htdocs</em>.
</td>
</tr>
</table>
</div>
</li>
</ol>
</div>
<div class="paragraph">
<p>To download Slim (or any other application) from its git repository, follow these steps:</p>
</div>
<div class="olist arabic">
<ol class="arabic">
<li>
<p>Ensure that your Apache server is running and that yopiu are logged in as a member of the <em>gitusers</em> group.</p>
</li>
<li>
<p>Obtain the repository&#8217;s clone URL. On Github, this can be easily obtained by visiting the project&#8217;s Github page and copying the clone URL from the right side of the page. For third-party or private git repositories, you can obtain the clone URL by asking the project team directly. Make a note of the clone URL, as you&#8217;ll need it in subsequent steps.</p>
</li>
</ol>
</div>
<div class="imageblock">
<div class="content">
<img src="./images/deploy-git-app/image4.png" alt="image4">
</div>
</div>
<div class="olist arabic">
<ol class="arabic">
<li>
<p>Open a new terminal.</p>
</li>
<li>
<p>Change to the <em>htdocs/</em> subdirectory of your XAMPP installation directory (typically, <em>/Applications/XAMPP</em>). Use the following command to clone the remote repository, remembering to replace the clone URL in the example below with the URL you obtained in the first step.</p>
<div class="literalblock">
<div class="content">
<pre>git clone https://github.com/slimphp/Slim.git</pre>
</div>
</div>
<div class="imageblock">
<div class="content">
<img src="./images/deploy-git-app/image5.png" alt="image5">
</div>
</div>
<div class="paragraph">
<p>The git client will begin downloading the code from the remote repository.</p>
</div>
</li>
<li>
<p>Once the process is complete, you should see a new directory containing the downloaded code, and you can now begin using it.</p>
<div class="paragraph">
<p>In this particular case, since the Slim framework includes a starter application, you can simply browse to the URL <a href="http://localhost/Slim" class="bare">http://localhost/Slim</a> and you should see the example application&#8217;s welcome page, as shown below.</p>
</div>
<div class="imageblock">
<div class="content">
<img src="./images/deploy-git-app/image6.png" alt="image6">
</div>
</div>
</li>
</ol>
</div>
<div class="admonitionblock note">
<table>
<tr>
<td class="icon">
<i class="fa icon-note" title="Note"></i>
</td>
<td class="content">
You can use the git command-line client to create a new branch of the codebase should you wish to make any changes to it. If you have appropriate privileges, you can also commit your changes back to the remote repository. <a href="http://git-scm.com/docs/gittutorial">Learn more about the git command-line client</a>.
</td>
</tr>
</table>
</div>
    </article>
  </div>
</div>

    </div>

    <footer class="footer row">
      <div class="columns">
        <div class="footer_lists-container row collapse">
          <div class="footer_social columns large-2">
            <ul class="social">
  <li class="twitter"><a href="https://twitter.com/apachefriends">Follow us on Twitter</a></li>
  <li class="facebook"><a href="https://www.facebook.com/we.are.xampp">Like us on Facebook</a></li>
</ul>

            <p class="footer_copyright">Copyright (c) 2022, Apache Friends</p>
          </div>
          <ul class="footer_links columns large-9">
            <li><a href="https://www.apachefriends.org/blog.html">Blog</a></li>
            <li><a href="/privacy_policy.html">Privacy Policy</a></li>
            <li>
<a target="_blank" href="http://www.fastly.com/">                CDN provided by
                <img width="48" data-2x="/dashboard/images/<EMAIL>" src="/dashboard/images/fastly-logo.png" />
</a>            </li>
          </ul>
        </div>
      </div>
    </footer>

    <!-- JS Libraries -->
    <script src="//code.jquery.com/jquery-1.10.2.min.js"></script>
    <script src="/dashboard/javascripts/all.js" type="text/javascript"></script>
  </body>
</html>
