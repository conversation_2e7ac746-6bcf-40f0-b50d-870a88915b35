<!doctype html>
<html lang="en">
  <head>
    <meta charset="utf-8">
    <!-- Always force latest IE rendering engine or request Chrome Frame -->
    <meta content="IE=edge,chrome=1" http-equiv="X-UA-Compatible">
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />

    <!-- Use title if it's in the page YAML frontmatter -->
    <title>Start a New Zend Framework 2 Project</title>

    
    

    <link href="/dashboard/stylesheets/normalize.css" rel="stylesheet" type="text/css" /><link href="/dashboard/stylesheets/all.css" rel="stylesheet" type="text/css" />
    <link href="//cdnjs.cloudflare.com/ajax/libs/font-awesome/3.1.0/css/font-awesome.min.css" rel="stylesheet" type="text/css" />

    <script src="/dashboard/javascripts/modernizr.js" type="text/javascript"></script>


    <link href="/dashboard/images/favicon.png" rel="icon" type="image/png" />


  </head>

  <body class="docs docs_create-framework-project-zf2">
    <div id="fb-root"></div>
    <script>(function(d, s, id) {
      var js, fjs = d.getElementsByTagName(s)[0];
      if (d.getElementById(id)) return;
      js = d.createElement(s); js.id = id;
      js.src = "//connect.facebook.net/en_US/all.js#xfbml=1&appId=277385395761685";
      fjs.parentNode.insertBefore(js, fjs);
    }(document, 'script', 'facebook-jssdk'));</script>
    <header class="header contain-to-grid">
      <nav class="top-bar" data-topbar>
        <ul class="title-area">
          <li class="name">
            <h1><a href="/dashboard/index.html">Apache Friends</a></h1>
          </li>
          <li class="toggle-topbar menu-icon">
            <a href="#">
              <span>Menu</span>
            </a>
          </li>
        </ul>

        <section class="top-bar-section">
          <!-- Left Nav Section -->
          <ul class="left">
              <li class="item "><a href="/dashboard/faq.html">FAQs</a></li>
              <li class="item active"><a href="/dashboard/howto.html">HOW-TO Guides</a></li>
              <li class="item "><a target="_blank" href="/dashboard/phpinfo.php">PHPInfo</a></li>
              <li class="item "><a href="/phpmyadmin/">phpMyAdmin</a></li>
          </ul>
        </section>
      </nav>
    </header>

    <div class="wrapper">
      <div class="hero">
  <div class="row">
    <div class="large-12 columns">
      <h1>Documentation</h1>
    </div>
  </div>
</div>
<div class="row">
  <div class="large-12 columns">
    <ul class="sub-nav">
      <li>
<a class="pdf" target="_blank" href="/dashboard/docs/create-framework-project-zf2.pdf">          Download PDF
          <span>create-framework-project-zf2.pdf</span>
</a>      </li>
    </ul>
    <article class="asciidoctor">
      <h1>Start a New Zend Framework 2 Project</h1>
<div class="paragraph">
<p>XAMPP makes it easy to start developing with PHP, and <a href="http://framework.zend.com/">Zend Framework 2</a> is one of the most popular PHP development frameworks. This guide walks you through the process of initializing a new Zend Framework 2 project with XAMPP.</p>
</div>
<div class="admonitionblock note">
<table>
<tr>
<td class="icon">
<i class="fa icon-note" title="Note"></i>
</td>
<td class="content">
This guide uses the command-line git client for Mac OS X. If you don&#8217;t already have this, you can install it easily by running the command <em>brew install git</em> from your terminal.  It also assumes that the new Zend Framework 2 application will be accessible at the URL <a href="http://localhost/myapp/" class="bare">http://localhost/myapp/</a>.
</td>
</tr>
</table>
</div>
<div class="paragraph">
<p>Follow these steps:</p>
</div>
<div class="olist arabic">
<ol class="arabic">
<li>
<p>Open a new terminal and ensure you are logged in as administrator.</p>
</li>
<li>
<p>Within your XAMPP installation directory (usually <em>/Applications/XAMPP/xamppfiles/</em>), create a new directory named <em>apps/</em> (if it doesn&#8217;t already exist). Then, within this new <em>apps/</em> directory, create a directory to hold your Zend Framework application and its related XAMPP configuration files. In this case, call the directory <em>myapp/</em>.</p>
<div class="literalblock">
<div class="content">
<pre>cd /Applications/XAMPP/xamppfiles/
mkdir apps
mkdir apps/myapp</pre>
</div>
</div>
</li>
<li>
<p>Clone the Zend Framework 2 sample application repository to the <em>myapp/</em> directory using <em>git</em>.</p>
<div class="literalblock">
<div class="content">
<pre>cd /Applications/XAMPP/xamppfiles/apps/myapp
git clone git://github.com/zendframework/ZendSkeletonApplication.git</pre>
</div>
</div>
<div class="imageblock">
<div class="content">
<img src="./images/create-framework-project-zf2/image1.png" alt="image1">
</div>
</div>
<div class="paragraph">
<p>This will produce a <em>ZendSkeletonApplication/</em> subdirectory in the <em>myapp/</em> directory. Rename this newly-created subdirectory to <em>htdocs</em>.</p>
</div>
<div class="literalblock">
<div class="content">
<pre>cd /Applications/XAMPP/xamppfiles/apps/myapp/
mv ZendSkeletonApplication htdocs</pre>
</div>
</div>
<div class="admonitionblock note">
<table>
<tr>
<td class="icon">
<i class="fa icon-note" title="Note"></i>
</td>
<td class="content">
This will be the main working directory for your Zend Framework 2 project.
</td>
</tr>
</table>
</div>
</li>
<li>
<p>Change to the <em>myapp/htdocs/</em> directory and run the following commands to update <a href="https://getcomposer.org/">Composer</a> (the PHP dependency manager) and install the Zend Framework 2 components.</p>
<div class="literalblock">
<div class="content">
<pre>cd /Applications/XAMPP/xamppfiles/apps/myapp/htdocs
/Applications/XAMPP/bin/php composer.phar self-update
/Applications/XAMPP/bin/php composer.phar install</pre>
</div>
</div>
<div class="paragraph">
<p>In case you encounter SSL errors when running the above commands, update the <em>/Applications/XAMPP/etc/php.ini</em> file and add the <em>openssl.cafile</em> variable to let PHP know where to find your system&#8217;s SSL certificates, then try again.</p>
</div>
<div class="literalblock">
<div class="content">
<pre>openssl.cafile=/Applications/XAMPP/xamppfiles/share/curl/curl-ca-bundle.crt</pre>
</div>
</div>
<div class="paragraph">
<p>Here&#8217;s an example of what you might see as Composer downloads and installs dependencies.</p>
</div>
<div class="imageblock">
<div class="content">
<img src="./images/create-framework-project-zf2/image2.png" alt="image2">
</div>
</div>
</li>
<li>
<p>Next, within the <em>myapp/</em> directory, create a new conf/ subdirectory.</p>
<div class="literalblock">
<div class="content">
<pre>cd /Applications/XAMPP/xamppfiles/apps/myapp
mkdir conf</pre>
</div>
</div>
<div class="olist loweralpha">
<ol class="loweralpha" type="a">
<li>
<p>Within the new <em>conf</em>/ subdirectory, use your text editor to create and populate a file named <em>httpd-prefix.conf</em> with the following content:</p>
<div class="literalblock">
<div class="content">
<pre>Alias /myapp/ "/Applications/XAMPP/xamppfiles/apps/myapp/htdocs/public/"
Alias /myapp "/Applications/XAMPP/xamppfiles/apps/myapp/htdocs/public"
Include "/Applications/XAMPP/xamppfiles/apps/myapp/conf/httpd-app.conf"</pre>
</div>
</div>
</li>
<li>
<p>Within the <em>conf/</em> subdirectory, also create and populate a file named <em>httpd-app.conf</em> with the following content:</p>
<div class="literalblock">
<div class="content">
<pre>&lt;Directory /Applications/XAMPP/xamppfiles/apps/myapp/htdocs/public&gt;
   Options +FollowSymLinks
   AllowOverride All
   Require all granted
&lt;/Directory&gt;</pre>
</div>
</div>
</li>
</ol>
</div>
</li>
<li>
<p>Edit the <em>httpd-xampp.conf</em> file in the <em>etc/extra/</em> subdirectory of your XAMPP installation directory and add the following line at the end to include the <em>httpd-prefix.conf</em> created earlier.</p>
<div class="literalblock">
<div class="content">
<pre>Include "/Applications/XAMPP/xamppfiles/apps/myapp/conf/httpd-prefix.conf"</pre>
</div>
</div>
<div class="admonitionblock note">
<table>
<tr>
<td class="icon">
<i class="fa icon-note" title="Note"></i>
</td>
<td class="content">
Remember to update the above file and directory paths so that they&#8217;re valid for your system.
</td>
</tr>
</table>
</div>
</li>
<li>
<p>Check that you have a directory structure like this:</p>
<div class="imageblock">
<div class="content">
<img src="./images/create-framework-project-zf2/image3.png" alt="image3">
</div>
</div>
</li>
<li>
<p>Restart the Apache server using the XAMPP control panel.</p>
</li>
<li>
<p>You should now be able to access the Zend Framework 2 skeleton application by browsing to <a href="http://localhost/myapp" class="bare">http://localhost/myapp</a>. Here is an example of the default welcome page you should see:</p>
<div class="imageblock">
<div class="content">
<img src="./images/create-framework-project-zf2/image4.png" alt="image4">
</div>
</div>
</li>
</ol>
</div>
<div class="paragraph">
<p>You can now begin developing your Zend Framework 2 application by modifying the skeleton application code. For more information, <a href="http://framework.zend.com/manual/2.3/en/user-guide/overview.html">refer to the Zend Framework 2 User Guide</a>.</p>
</div>
    </article>
  </div>
</div>

    </div>

    <footer class="footer row">
      <div class="columns">
        <div class="footer_lists-container row collapse">
          <div class="footer_social columns large-2">
            <ul class="social">
  <li class="twitter"><a href="https://twitter.com/apachefriends">Follow us on Twitter</a></li>
  <li class="facebook"><a href="https://www.facebook.com/we.are.xampp">Like us on Facebook</a></li>
</ul>

            <p class="footer_copyright">Copyright (c) 2022, Apache Friends</p>
          </div>
          <ul class="footer_links columns large-9">
            <li><a href="https://www.apachefriends.org/blog.html">Blog</a></li>
            <li><a href="/privacy_policy.html">Privacy Policy</a></li>
            <li>
<a target="_blank" href="http://www.fastly.com/">                CDN provided by
                <img width="48" data-2x="/dashboard/images/<EMAIL>" src="/dashboard/images/fastly-logo.png" />
</a>            </li>
          </ul>
        </div>
      </div>
    </footer>

    <!-- JS Libraries -->
    <script src="//code.jquery.com/jquery-1.10.2.min.js"></script>
    <script src="/dashboard/javascripts/all.js" type="text/javascript"></script>
  </body>
</html>
