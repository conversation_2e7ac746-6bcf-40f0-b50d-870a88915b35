<!doctype html>
<html lang="fr">
  <head>
    <meta charset="utf-8">
    <!-- Always force latest IE rendering engine or request Chrome Frame -->
    <meta content="IE=edge,chrome=1" http-equiv="X-UA-Compatible">
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />

    <!-- Use title if it's in the page YAML frontmatter -->
    <title>XAMPP FAQs for Mac OS X</title>

    <meta name="description" content="Instructions on how to install XAMPP for OSX distributions." />
    

    <link href="/dashboard/stylesheets/normalize.css" rel="stylesheet" type="text/css" /><link href="/dashboard/stylesheets/all.css" rel="stylesheet" type="text/css" />
    <link href="//cdnjs.cloudflare.com/ajax/libs/font-awesome/3.1.0/css/font-awesome.min.css" rel="stylesheet" type="text/css" />

    <script src="/dashboard/javascripts/modernizr.js" type="text/javascript"></script>


    <link href="/dashboard/images/favicon.png" rel="icon" type="image/png" />


  </head>

  <body class="fr fr_faq">
    <div id="fb-root"></div>
    <script>(function(d, s, id) {
      var js, fjs = d.getElementsByTagName(s)[0];
      if (d.getElementById(id)) return;
      js = d.createElement(s); js.id = id;
      js.src = "//connect.facebook.net/en_US/all.js#xfbml=1&appId=277385395761685";
      fjs.parentNode.insertBefore(js, fjs);
    }(document, 'script', 'facebook-jssdk'));</script>
    <header class="header contain-to-grid">
      <nav class="top-bar" data-topbar>
        <ul class="title-area">
          <li class="name">
            <h1><a href="/dashboard/fr/index.html">Apache Friends</a></h1>
          </li>
          <li class="toggle-topbar menu-icon">
            <a href="#">
              <span>Menu</span>
            </a>
          </li>
        </ul>

        <section class="top-bar-section">
          <!-- Left Nav Section -->
          <ul class="left">
              <li class="item active"><a href="/dashboard/fr/faq.html">Questions fréquentes</a></li>
              <li class="item "><a href="/dashboard/fr/howto.html">HOW-TO Guides</a></li>
              <li class="item "><a target="_blank" href="/dashboard/phpinfo.php">PHPInfo</a></li>
              <li class="item "><a href="/phpmyadmin/">phpMyAdmin</a></li>
          </ul>
        </section>
      </nav>
    </header>

    <div class="wrapper">
      <div class="hero">
  <div class="row">
    <div class="large-12 columns">
      <h1>OS X <span>Questions Fréquentes</span></h1>
    </div>
  </div>
</div>
<div class="row">
    <div class="large-8 columns">
    <dl class="accordion">
    
      <dt>What is the difference between XAMPP for OS X and XAMPP-VM?</dt>
      <dd>
        <p>
        <ul>
          <li>XAMPP for OS X is a native installer for OS X. It installs Apache, PHP and other XAMPP components directly on your OS X system, in the /Applications/XAMPP folder.</li>
          <li>XAMPP-VM is a virtual machine for OS X. It includes Apache, PHP and other XAMPP components and runs them in a Linux-based virtual machine on your OS X system.</li>
        </ul>
        </p>
        <p>For more information, refer to the blog post at <a href="https://www.apachefriends.org/blog/new_xampp_20170628.html">https://www.apachefriends.org/blog/new_xampp_20170628.html</a>.</p>.
      </dd> 
    
      <dt>Comment installer XAMPP sur Mac OS X?</dt>
      <dd>
      <p>Pour installer XAMPP, faites comme suit:</p>
      <ul>
        <li>Ouvrez l'image DMG.</li>
        <li>Démarrez l'installation par un double-click sur l'image.</li></ul>
      <p>C'est tout. XAMPP est installé sous le répertoire /Applications/XAMPP.</p>
      </dd>
      <dt>Does XAMPP include MySQL or MariaDB?</dt>
      <dd>
        <p>Since XAMPP 5.5.30 and 5.6.14, XAMPP ships MariaDB instead of MySQL. The commands and tools are the same for both.</p>
      </dd>
      <dt>Comment lancer XAMMP ?</dt>
      <dd>
      <p>Pour lancer XAMPP, ouvrez le Contrôle XAMPP et lancez Apache, MySQL et ProFTPD. Le nom du Contrôle XAMPP est "manager-osx".</p>      
      </dd>
      <dt>Stopper XAMPP ?</dt>
      <dd>
      <p>Pour arrêter XAMPP ouvrez le Contrôle de XAMPP et arrêtez les serveurs. Le nom du Contrôle est "manager-osx".</p>      
      </dd>
      <dt>Comment tester si tout a fonctionné ?</dt>
      <dd>
      <p>Tapez l'URL suivante dans un navigateur web:</p>
      <p><code>http://localhost</code></p>

      <p>You should see the XAMPP start page, as shown below.</p>
        <img src="/dashboard/images/screenshots/xampp-macosx-start.jpg" />    
      </dd>
      <dt>XAMPP destiné à la production?</dt>
      <dd><p>XAMPP est destiné exclusivement aux développeurs. La manière dont XAMPP est configuré donne un maximum de libertés en cours de développement. Ceci permet de gagner du temps en phase de développement, mais peut être fatal en environnement de production.</p>
      <p>Liste des trous de sécurité dans XAMPP:</p>
      <ol>
        <li>L'administrateur MySQL (root) sans mot de passe.</li>
        <li>Le démon MySQL est accessible via le réseau.</li>
        <li>ProFTPD utilise le mot de passe "lampp" pour l'utilisateur "daemon".</li>
      </ol>
      <p>Pour corriger la plupart des failles de sécurité, utilisez cette commande:</p>
      <p><code>sudo /Applications/XAMPP/xamppfiles/xampp security</code></p>
      <p>Cela lancera une petite vérification pour sécuriser l'installation de XAMPP.</p></dd>

      <dt>Que signifient les messages d'erreur que je vois au lancement de XAMPP ?</dt>
      <dd>
        <p>Les messages d'erreur possibles au démarrage de XAMPP :</p>
        <p><code>LAMPP-Apache is already running.<br />
            An Apache daemon is already running.</code></p>
        <p>Le démarrage de XAMPP-Apache avec le script LAMPP a échoué car une instance d'Apache fonctionne déjà. Arrêter ce service pour démarrer XAMPP correctement.</p>
        <p><code>LAMPP-MySQL is already running.<br />
            A MySQL daemon is already running.</code></p>
        <p>Ceci est dû principalement aux mêmes raisons que l'erreur ci-dessus. Un service MySQL fonctionne déjà sur votre système. Arrêter ce service pour démarrer LAMPP correctement.</p>
      </dd>

      <dt>Apache doesn't seem to start. What can I do?</dt>
      <dd>
        <p>Cette erreur peut avoir plusieurs causes. Apache affiche cette erreur dans plusieurs situations. Pour trouver la cause exacte, faisons quelques recherches :</p>
        <p><code>tail -2 /Applications/XAMPP/logs/error_log</code></p>
        <p>De l'aide pour un message d'erreur ? Visitez <a href="/community.html">nos pages de la communauté</a>.</p>
      </dd>

      <dt>Comment mieux sécuriser mon installation XAMPP ?</dt>
      <dd>
        <p>Après installation, XAMPP est accessible à tous sans mot de passe et ceci n'est pas recommandé.</p>
        <p>Pour lancer un test de sécurité simple, taper cette commande (en tant que root):</p>
        <p><code>sudo /Applications/XAMPP/xamppfiles/xampp security</code></p>
        <p>Maintenant, vous devriez voir ce dialogue à l'écran :</p>
        <p><code>
XAMPP: Quick security check...</br>
XAMPP: MySQL is accessable via network.</br>
XAMPP: Normaly that's not recommended. Do you want me to turn it off? [yes] yes</br>
XAMPP: Turned off.</br>
XAMPP: Stopping MySQL...</br>
XAMPP: Starting MySQL...</br>
XAMPP: The MySQL/phpMyAdmin user pma has no password set!!!</br>
XAMPP: Do you want to set a password? [yes] yes</br>
XAMPP: Password: ******</br>
XAMPP: Password (again): ******</br>
XAMPP: Setting new MySQL pma password.</br>
XAMPP: Setting phpMyAdmin's pma password to the new one.</br>
XAMPP: MySQL has no root passwort set!!!</br>
XAMPP: Do you want to set a password? [yes] yes</br>
XAMPP: Write the passworde somewhere down to make sure you won't forget it!!!</br>
XAMPP: Password: ******</br>
XAMPP: Password (again): ******</br>
XAMPP: Setting new MySQL root password.</br>
XAMPP: Setting phpMyAdmin's root password to the new one.</br>
XAMPP: The FTP password for user 'nobody' is still set to 'lampp'.</br>
XAMPP: Do you want to change the password? [yes] yes</br>
XAMPP: Password: ******</br>
XAMPP: Password (again): ******</br>
XAMPP: Reload ProFTPD...</br>
XAMPP: Done.</br>
  </code></p>
        <p>(1) Choisir un mot de passe protégera les pages de démo XAMPP (http://localhost/xampp/) par ce mot de passe. L'utilisateur est 'lampp'!</p>
        <p>L'exécution de cette commande devrait rendre l'installation XAMPP plus sûre.</p>
      </dd>

      <dt>Comment activer l'extension OCI8/Oracle pour PHP ?</dt>
      <dd>
        <p>Pour activer l'extension OCI8/Oracle pour PHP, exécutez cette commande:</p>
        <p><code>sudo /Applications/XAMPP/xamppfiles/lampp oci8</code></p>
        <p>Ce dialogue commencera :</p>
        <p><code>Please enter the path to your Oracle or Instant Client installation:</br>
[/Applications/XAMPP/xamppfiles/lib/instantclient-********.0] </br>
installing symlinks...</br>
patching php.ini...</br>
OCI8 add-on activation likely successful.</br>
LAMPP: Stopping Apache with SSL...</br>
LAMPP: Starting Apache with SSL...</code></p>
        <p>Alors l'extension devrait être active.</p>
      </dd>

      <dt>How do I enable access to phpMyAdmin from the outside?</dt>
      <dd>
        <p>In the basic configuration of XAMPP, phpMyAdmin is accessible only from the same host that XAMPP is running on, at http://127.0.0.1 or http://localhost.</p>
        <p>IMPORTANT: Enabling external access for phpMyAdmin in production environments is a significant security risk. You are strongly advised to only allow access from localhost. A remote attacker could take advantage of any existing vulnerability for executing code or for modifying your data.</p>
        <p>To enable remote access to phpMyAdmin, follow these steps:</p>
        <ul>
          <li>Edit the xamppfiles/etc/extra/httpd-xampp.conf file in your XAMPP installation directory.</li>
          <li>Within this file, find the lines below. 
            <p><code>
                Alias /phpmyadmin "/Applications/XAMPP/xamppfiles/phpmyadmin"
                &lt;Directory "/Applications/XAMPP/xamppfiles/phpmyadmin"&gt;
                  AllowOverride AuthConfig
                  Require local
            </code></p>
          </li>
          <li>Then replace 'Require local' with 'Require all granted'.</li>
            <p><code>
                Alias /phpmyadmin "/Applications/XAMPP/xamppfiles/phpmyadmin"
                &lt;Directory "/Applications/XAMPP/xamppfiles/phpmyadmin"&gt;
                  AllowOverride AuthConfig
                  Require all granted
            </code></p>
          <li>Restart the Apache server using the XAMPP control panel.</li>
        </ul>
      </dd>

      <dt>Where are the main XAMPP configuration files?</dt>
      <dd>
        <p>The main XAMPP configuration files are located as follows:</p>
        <ul>
          <li>Apache configuration file: /Applications/XAMPP/xamppfiles/etc/httpd.conf, /Applications/XAMPP/xamppfiles/etc/extra/httpd-xampp.conf</li>
          <li>PHP configuration file: /Applications/XAMPP/xamppfiles/etc/php.ini</li>
          <li>MySQL configuration file: /Applications/XAMPP/xamppfiles/etc/my.cnf</li>
          <li>ProFTPD configuration file: /Applications/XAMPP/xamppfiles/etc/proftpd.conf</li>
        </ul>
      </dd>

      <dt>How do I send email with XAMPP?</dt>
      <dd>
        <p>To send email with XAMPP, use the PEAR Mail and Net_SMTP packages, which allow you to send email using an external SMTP account (such as a Gmail account). Follow these steps:</p>
        <ul>
          <li>Install the Mail and Net_SMTP PEAR modules:
          <code>
          pear install Net_SMTP Mail
          </code>
          Note that if these packages are already installed in your system you see the messages below when executing that command:
          <code>
          Ignoring installed package pear/Net_SMTP
          Ignoring installed package pear/Mail
          Nothing to install
          </code>
          </li>
          <li>
          Create the following example script in your "htdocs" directory to send an email:
          <code>
          &lt;?php
          require_once "Mail.php";

          $from = "<EMAIL>";
          $to = '<EMAIL>';

          $host = "ssl://smtp.gmail.com";
          $port = "465";
          $username = '<EMAIL>';
          $password = 'your-gmail-password';

          $subject = "test";
          $body = "test";

          $headers = array ('From' => $from, 'To' => $to,'Subject' => $subject);
          $smtp = Mail::factory('smtp',
             array ('host' => $host,
               'port' => $port,
               'auth' => true,
               'username' => $username,
               'password' => $password));

          $mail = $smtp->send($to, $headers, $body);

          if (PEAR::isError($mail)) {
            echo($mail->getMessage());
          } else {
            echo("Message successfully sent!\n");
          }
          ?>
          </code>
          <p>Remember to replace the dummy values shown with your actual Gmail address and account password. If you don't plan to use Gmail's SMTP server, replace the SMTP host details with appropriate values for your organization or ISP's SMTP server.</p>
          </li>
          <li>
          Execute the script by browsing to it using your Web browser. You should see a notification that the message was successfully sent, and the message should be delivered to the recipient email address.
          </li>
        </ul>
      </dd>
      
      <dt>Sauvegarder/Restaurer mon système XAMPP ?</dt>
      <dd>
        <p><strong>Attention:</strong> La fonction sauvegarde/restauration est en développement et pourrait dysfonctionner.</p>
        <p>Vous créerai la sauvegarde en appelant:</p>
        <p><code>sudo /Applications/XAMPP/xamppfiles/xampp backup</code></p>
        <p>ou</p>
        <p><code>sudo /Applications/XAMPP/xamppfiles/xampp backup secret</code></p>
        <p>Quand "secret" est le mot de passe root pour MySQL. Cette commande fera cette sortie:</p>
        <p><code>Backing up databases...</br>
Backing up configuration, log and htdocs files...</br>
Calculating checksums...</br>
Building final backup file...</br>
Backup finished.</br>
Take care of /Applications/XAMPP/xamppfiles/backup/xampp-backup-22-01-14.sh</code></p>

        <p>Le fichier avec vos données de sauvegarde est /Applications/XAMPP/xamppfiles/backup/xampp-backup-22-01-14.sh (de l'exemple ci-dessus). Placez-le à endroit sûr.</p>

        <p>La nouvelle machine doit disposer de la même version d'XAMPP que la machine d'origine/source.</p>
        <p><code>sudo sh xampp-backup-22-01-14.sh</code></p>
        <p>Vous devriez voir quelques chose comme ceci:</p>
        <p><code>Checking integrity of files...</br>
Restoring configuration, log and htdocs files...</br>
Checking versions...</br>
Installed: XAMPP 1.4.2</br>
Backup from: XAMPP 1.4.2</br>
Restoring MySQL databases...</br>
Restoring MySQL user databases...</br>
Backup complete. Have fun!</br>
You may need to restart XAMPP to complete the restore.</br>
  </code></p>
        <p>C'est tout. Retenez que c'est une fonction beta.</p>
      </dd>
    </dl>

  </div>
</div>

    </div>

    <footer class="footer row">
      <div class="columns">
        <div class="footer_lists-container row collapse">
          <div class="footer_social columns large-2">
            <ul class="social">
  <li class="twitter"><a href="https://twitter.com/apachefriends">Follow us on Twitter</a></li>
  <li class="facebook"><a href="https://www.facebook.com/we.are.xampp">Like us on Facebook</a></li>
</ul>

            <p class="footer_copyright">Copyright (c) 2022, Apache Friends</p>
          </div>
          <ul class="footer_links columns large-9">
            <li><a href="https://www.apachefriends.org/blog.html">Blog</a></li>
            <li><a href="/privacy_policy.html">Politique de confidentialité</a></li>
            <li>
<a target="_blank" href="http://www.fastly.com/">                CDN fourni par
                <img width="48" data-2x="/dashboard/images/<EMAIL>" src="/dashboard/images/fastly-logo.png" />
</a>            </li>
          </ul>
        </div>
      </div>
    </footer>

    <!-- JS Libraries -->
    <script src="//code.jquery.com/jquery-1.10.2.min.js"></script>
    <script src="/dashboard/javascripts/all.js" type="text/javascript"></script>
  </body>
</html>
