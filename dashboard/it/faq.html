<!doctype html>
<html lang="it">
  <head>
    <meta charset="utf-8">
    <!-- Always force latest IE rendering engine or request Chrome Frame -->
    <meta content="IE=edge,chrome=1" http-equiv="X-UA-Compatible">
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />

    <!-- Use title if it's in the page YAML frontmatter -->
    <title>XAMPP FAQs for Mac OS X</title>

    <meta name="description" content="Instructions on how to install XAMPP for OSX distributions." />
    

    <link href="/dashboard/stylesheets/normalize.css" rel="stylesheet" type="text/css" /><link href="/dashboard/stylesheets/all.css" rel="stylesheet" type="text/css" />
    <link href="//cdnjs.cloudflare.com/ajax/libs/font-awesome/3.1.0/css/font-awesome.min.css" rel="stylesheet" type="text/css" />

    <script src="/dashboard/javascripts/modernizr.js" type="text/javascript"></script>


    <link href="/dashboard/images/favicon.png" rel="icon" type="image/png" />


  </head>

  <body class="it it_faq">
    <div id="fb-root"></div>
    <script>(function(d, s, id) {
      var js, fjs = d.getElementsByTagName(s)[0];
      if (d.getElementById(id)) return;
      js = d.createElement(s); js.id = id;
      js.src = "//connect.facebook.net/en_US/all.js#xfbml=1&appId=277385395761685";
      fjs.parentNode.insertBefore(js, fjs);
    }(document, 'script', 'facebook-jssdk'));</script>
    <header class="header contain-to-grid">
      <nav class="top-bar" data-topbar>
        <ul class="title-area">
          <li class="name">
            <h1><a href="/dashboard/it/index.html">Apache Friends</a></h1>
          </li>
          <li class="toggle-topbar menu-icon">
            <a href="#">
              <span>Menu</span>
            </a>
          </li>
        </ul>

        <section class="top-bar-section">
          <!-- Left Nav Section -->
          <ul class="left">
              <li class="item active"><a href="/dashboard/it/faq.html">Domande frequenti</a></li>
              <li class="item "><a href="/dashboard/it/howto.html">HOW-TO Guides</a></li>
              <li class="item "><a target="_blank" href="/dashboard/phpinfo.php">PHPInfo</a></li>
              <li class="item "><a href="/phpmyadmin/">phpMyAdmin</a></li>
          </ul>
        </section>
      </nav>
    </header>

    <div class="wrapper">
      <div class="hero">
  <div class="row">
    <div class="large-12 columns">
      <h1>OS X <span>Domande frequenti</span></h1>
    </div>
  </div>
</div>
<div class="row">
    <div class="large-8 columns">
    <dl class="accordion">
    
      <dt>What is the difference between XAMPP for OS X and XAMPP-VM?</dt>
      <dd>
        <p>
        <ul>
          <li>XAMPP for OS X is a native installer for OS X. It installs Apache, PHP and other XAMPP components directly on your OS X system, in the /Applications/XAMPP folder.</li>
          <li>XAMPP-VM is a virtual machine for OS X. It includes Apache, PHP and other XAMPP components and runs them in a Linux-based virtual machine on your OS X system.</li>
        </ul>
        </p>
        <p>For more information, refer to the blog post at <a href="https://www.apachefriends.org/blog/new_xampp_20170628.html">https://www.apachefriends.org/blog/new_xampp_20170628.html</a>.</p>.
      </dd> 
    
      <dt>Come faccio installare XAMPP per Mac OS X?</dt>
      <dd>
      <p>Per installare XAMPP basta procedere nel modo seguente:</p>
      <ul>
        <li>Aprire il DMG-immagine.</li>
        <li>Fare doppio clic sull' file immagine per avviare il processo di installazione.</li></ul>
      <p>Questo è tutto. XAMPP è ora installato sotto la cartella /Applications/XAMPP.</p>
      </dd>
      <dt>Does XAMPP include MySQL or MariaDB?</dt>
      <dd>
        <p>Since XAMPP 5.5.30 and 5.6.14, XAMPP ships MariaDB instead of MySQL. The commands and tools are the same for both.</p>
      </dd>
      <dt>Come posso avviare XAMPP?</dt>
      <dd>
      <p>Per avviare XAMPP è sufficiente aprire XAMPP controll e avviare Apache, MySQL e ProFTPD. Il nome del XAMPP controll è 'manager-osx'.</p>      
      </dd>
      <dt>Come faccio a fermare XAMPP?</dt>
      <dd>
      <p>Per fermare XAMPP è sufficiente aprire XAMPP controll e fermare i server. Il nome del controllo XAMPP è 'manager-osx'.</p>      
      </dd>
      <dt>Come posso verificare che tutto funzioni?</dt>
      <dd>
      <p>Digitare il seguente URL in un browser Web</p>
      <p><code>http://localhost</code></p>

      <p>You should see the XAMPP start page, as shown below.</p>
        <img src="/dashboard/images/screenshots/xampp-macosx-start.jpg" />    
      </dd>
      <dt>Il prodotto XAMPP e' pronto?</dt>
      <dd><p>XAMPP non è inteso per la produzione ma solo per ambienti di sviluppo. Il modo in cui XAMPP è configurato è quello di essere più aperto possibile per permettere allo sviluppatore lui/lei di personalizzarlo. Per ambienti di sviluppo questo è ottimo ma in un ambiente di produzione potrebbe essere pericoloso.</p>
      <p>Ecco una lista delle impostazioni di sicurezza mancanti in XAMPP:</p>
      <ol>
        <li>L'amministratore di MySQL (root) non ha password.</li>
        <li>Il demone MySQL è accessibile via rete.</li>
        <li>ProFTPD usa come password "lampp" e come user "daemon".</li>
      </ol>
      <p>Per risolvere la maggior parte delle carenze di sicurezza e' sufficiente digitare il seguente comando</p>
      <p><code>sudo /Applications/XAMPP/xamppfiles/xampp security</code></p>
      <p>Si darà il via a un piccolo controllo di sicurezza per rendere sicura l'installazione XAMPP.</p></dd>

      <dt>Qual è il significato dei messaggi di errore che vedo quando si avvia XAMPP?</dt>
      <dd>
        <p>È possibile ottenere diversi messaggi di errore durante l'avvio di XAMPP</p>
        <p><code>LAMPP-Apache is already running.<br />
            An Apache daemon is already running.</code></p>
        <p>Lo script di avvio LAMPP non è stato avviato XAMPP-Apache perché c'è un 'istanza di Apache già in esecuzione. Per avviare XAMPP correttamente, prima devi fermare questo demone.</p>
        <p><code>LAMPP-MySQL is already running.<br />
            A MySQL daemon is already running.</code></p>
        <p>Ciò è in gran parte dovuto alle stesse ragioni all'errore precedente. Lo script di avvio LAMPP trovato un demone MySQL già in esecuzione sul vostro sistema. Per avviare correttamente LAMPP, prima devi fermare questo demone.</p>
      </dd>

      <dt>Apache doesn't seem to start. What can I do?</dt>
      <dd>
        <p>Questo errore può esistere per diverse ragioni. Apache viene visualizzato questo errore in diverse circostanze. Per trovare l'esatta ragione dobbiamo fare qualche ricerca</p>
        <p><code>tail -2 /Applications/XAMPP/logs/error_log</code></p>
        <p>Se ricevete un qualsiasi messaggio d'errore visitate <a href="/community.html">le nostre pagine di comunità</a> per ricevere aiuto.</p>
      </dd>

      <dt>Come posso rendere la mia installazione XAMPP più sicura?</dt>
      <dd>
        <p>In una installazione predefinita, XAMPP non ha password impostate e non è consigliabile eseguire XAMPP con questa configurazione essendo accessibile da altri.</p>
        <p>Basta digitare il seguente comando (come root) per avviare un semplice controllo di sicurezza</p>
        <p><code>sudo /Applications/XAMPP/xamppfiles/xampp security</code></p>
        <p>Ora si dovrebbe vedere la seguente finestra di dialogo sullo schermo</p>
        <p><code>
XAMPP: Quick security check...</br>
XAMPP: MySQL is accessable via network.</br>
XAMPP: Normaly that's not recommended. Do you want me to turn it off? [yes] yes</br>
XAMPP: Turned off.</br>
XAMPP: Stopping MySQL...</br>
XAMPP: Starting MySQL...</br>
XAMPP: The MySQL/phpMyAdmin user pma has no password set!!!</br>
XAMPP: Do you want to set a password? [yes] yes</br>
XAMPP: Password: ******</br>
XAMPP: Password (again): ******</br>
XAMPP: Setting new MySQL pma password.</br>
XAMPP: Setting phpMyAdmin's pma password to the new one.</br>
XAMPP: MySQL has no root passwort set!!!</br>
XAMPP: Do you want to set a password? [yes] yes</br>
XAMPP: Write the passworde somewhere down to make sure you won't forget it!!!</br>
XAMPP: Password: ******</br>
XAMPP: Password (again): ******</br>
XAMPP: Setting new MySQL root password.</br>
XAMPP: Setting phpMyAdmin's root password to the new one.</br>
XAMPP: The FTP password for user 'nobody' is still set to 'lampp'.</br>
XAMPP: Do you want to change the password? [yes] yes</br>
XAMPP: Password: ******</br>
XAMPP: Password (again): ******</br>
XAMPP: Reload ProFTPD...</br>
XAMPP: Done.</br>
  </code></p>
        <p>(1) L'impostazione di una password proteggerà le pagine dimostrative di XAMPP (http://localhost/xampp/) usando questa password. Il nome utente è 'lampp'!</p>
        <p>Dopo aver digitato questo comando la tua installazione XAMPP dovrebbe essere più sicuro.</p>
      </dd>

      <dt>Come faccio ad attivare l'estensione OCI8/Oracle per PHP?</dt>
      <dd>
        <p>Per attivare l'estensione OCI8/Oracle per PHP si prega di eseguire il seguente comando:</p>
        <p><code>sudo /Applications/XAMPP/xamppfiles/lampp oci8</code></p>
        <p>Apparirà la seguente finestra di dialogo:</p>
        <p><code>Please enter the path to your Oracle or Instant Client installation:</br>
[/Applications/XAMPP/xamppfiles/lib/instantclient-********.0] </br>
installing symlinks...</br>
patching php.ini...</br>
OCI8 add-on activation likely successful.</br>
LAMPP: Stopping Apache with SSL...</br>
LAMPP: Starting Apache with SSL...</code></p>
        <p>Ora l'estensione dovrebbe essere attiva.</p>
      </dd>

      <dt>How do I enable access to phpMyAdmin from the outside?</dt>
      <dd>
        <p>In the basic configuration of XAMPP, phpMyAdmin is accessible only from the same host that XAMPP is running on, at http://127.0.0.1 or http://localhost.</p>
        <p>IMPORTANT: Enabling external access for phpMyAdmin in production environments is a significant security risk. You are strongly advised to only allow access from localhost. A remote attacker could take advantage of any existing vulnerability for executing code or for modifying your data.</p>
        <p>To enable remote access to phpMyAdmin, follow these steps:</p>
        <ul>
          <li>Edit the xamppfiles/etc/extra/httpd-xampp.conf file in your XAMPP installation directory.</li>
          <li>Within this file, find the lines below. 
            <p><code>
                Alias /phpmyadmin "/Applications/XAMPP/xamppfiles/phpmyadmin"
                &lt;Directory "/Applications/XAMPP/xamppfiles/phpmyadmin"&gt;
                  AllowOverride AuthConfig
                  Require local
            </code></p>
          </li>
          <li>Then replace 'Require local' with 'Require all granted'.</li>
            <p><code>
                Alias /phpmyadmin "/Applications/XAMPP/xamppfiles/phpmyadmin"
                &lt;Directory "/Applications/XAMPP/xamppfiles/phpmyadmin"&gt;
                  AllowOverride AuthConfig
                  Require all granted
            </code></p>
          <li>Restart the Apache server using the XAMPP control panel.</li>
        </ul>
      </dd>

      <dt>Where are the main XAMPP configuration files?</dt>
      <dd>
        <p>The main XAMPP configuration files are located as follows:</p>
        <ul>
          <li>Apache configuration file: /Applications/XAMPP/xamppfiles/etc/httpd.conf, /Applications/XAMPP/xamppfiles/etc/extra/httpd-xampp.conf</li>
          <li>PHP configuration file: /Applications/XAMPP/xamppfiles/etc/php.ini</li>
          <li>MySQL configuration file: /Applications/XAMPP/xamppfiles/etc/my.cnf</li>
          <li>ProFTPD configuration file: /Applications/XAMPP/xamppfiles/etc/proftpd.conf</li>
        </ul>
      </dd>

      <dt>How do I send email with XAMPP?</dt>
      <dd>
        <p>To send email with XAMPP, use the PEAR Mail and Net_SMTP packages, which allow you to send email using an external SMTP account (such as a Gmail account). Follow these steps:</p>
        <ul>
          <li>Install the Mail and Net_SMTP PEAR modules:
          <code>
          pear install Net_SMTP Mail
          </code>
          Note that if these packages are already installed in your system you see the messages below when executing that command:
          <code>
          Ignoring installed package pear/Net_SMTP
          Ignoring installed package pear/Mail
          Nothing to install
          </code>
          </li>
          <li>
          Create the following example script in your "htdocs" directory to send an email:
          <code>
          &lt;?php
          require_once "Mail.php";

          $from = "<EMAIL>";
          $to = '<EMAIL>';

          $host = "ssl://smtp.gmail.com";
          $port = "465";
          $username = '<EMAIL>';
          $password = 'your-gmail-password';

          $subject = "test";
          $body = "test";

          $headers = array ('From' => $from, 'To' => $to,'Subject' => $subject);
          $smtp = Mail::factory('smtp',
             array ('host' => $host,
               'port' => $port,
               'auth' => true,
               'username' => $username,
               'password' => $password));

          $mail = $smtp->send($to, $headers, $body);

          if (PEAR::isError($mail)) {
            echo($mail->getMessage());
          } else {
            echo("Message successfully sent!\n");
          }
          ?>
          </code>
          <p>Remember to replace the dummy values shown with your actual Gmail address and account password. If you don't plan to use Gmail's SMTP server, replace the SMTP host details with appropriate values for your organization or ISP's SMTP server.</p>
          </li>
          <li>
          Execute the script by browsing to it using your Web browser. You should see a notification that the message was successfully sent, and the message should be delivered to the recipient email address.
          </li>
        </ul>
      </dd>
      
      <dt>Come faccio il backup/ripristino mio sistema XAMPP?</dt>
      <dd>
        <p><strong>Attenzione:</strong> Il backup e ripristinare la funzionalità è ancora in fase di sviluppo e potrebbero non funzionare correttamente.</p>
        <p>È possibile creare il backup digitando</p>
        <p><code>sudo /Applications/XAMPP/xamppfiles/xampp backup</code></p>
        <p>oppure</p>
        <p><code>sudo /Applications/XAMPP/xamppfiles/xampp backup secret</code></p>
        <p>Dove "secret" è la password di root di MySQL. Questo comando creerà il seguente output:</p>
        <p><code>Backing up databases...</br>
Backing up configuration, log and htdocs files...</br>
Calculating checksums...</br>
Building final backup file...</br>
Backup finished.</br>
Take care of /Applications/XAMPP/xamppfiles/backup/xampp-backup-22-01-14.sh</code></p>

        <p>Il file /Applications/XAMPP/xamppfiles/backup/xampp-backup-22-01-14.sh (nell'esempio sopra) contiene i dati backuped. Tieni questo file in un luogo sicuro.</p>

        <p>Sulla nuova macchina hai bisogno della stessa versione di XAMPP come sulla macchina originale / sorgente.</p>
        <p><code>sudo sh xampp-backup-22-01-14.sh</code></p>
        <p>Dovresti vedere qualcosa come questo</p>
        <p><code>Checking integrity of files...</br>
Restoring configuration, log and htdocs files...</br>
Checking versions...</br>
Installed: XAMPP 1.4.2</br>
Backup from: XAMPP 1.4.2</br>
Restoring MySQL databases...</br>
Restoring MySQL user databases...</br>
Backup complete. Have fun!</br>
You may need to restart XAMPP to complete the restore.</br>
  </code></p>
        <p>Questo è tutto. Tenete in mente che si tratta di una caratteristica beta.</p>
      </dd>
    </dl>

  </div>
</div>

    </div>

    <footer class="footer row">
      <div class="columns">
        <div class="footer_lists-container row collapse">
          <div class="footer_social columns large-2">
            <ul class="social">
  <li class="twitter"><a href="https://twitter.com/apachefriends">Follow us on Twitter</a></li>
  <li class="facebook"><a href="https://www.facebook.com/we.are.xampp">Like us on Facebook</a></li>
</ul>

            <p class="footer_copyright">Copyright (c) 2022, Apache Friends</p>
          </div>
          <ul class="footer_links columns large-9">
            <li><a href="https://www.apachefriends.org/blog.html">Blog</a></li>
            <li><a href="/privacy_policy.html">Politica sulla privacy</a></li>
            <li>
<a target="_blank" href="http://www.fastly.com/">                CDN fornito da
                <img width="48" data-2x="/dashboard/images/<EMAIL>" src="/dashboard/images/fastly-logo.png" />
</a>            </li>
          </ul>
        </div>
      </div>
    </footer>

    <!-- JS Libraries -->
    <script src="//code.jquery.com/jquery-1.10.2.min.js"></script>
    <script src="/dashboard/javascripts/all.js" type="text/javascript"></script>
  </body>
</html>
