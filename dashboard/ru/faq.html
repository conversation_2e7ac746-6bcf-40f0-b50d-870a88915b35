<!doctype html>
<html lang="ru">
  <head>
    <meta charset="utf-8">
    <!-- Always force latest IE rendering engine or request Chrome Frame -->
    <meta content="IE=edge,chrome=1" http-equiv="X-UA-Compatible">
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />

    <!-- Use title if it's in the page YAML frontmatter -->
    <title>XAMPP FAQs for Mac OS X</title>

    <meta name="description" content="Instructions on how to install XAMPP for OSX distributions." />
    

    <link href="/dashboard/stylesheets/normalize.css" rel="stylesheet" type="text/css" /><link href="/dashboard/stylesheets/all.css" rel="stylesheet" type="text/css" />
    <link href="//cdnjs.cloudflare.com/ajax/libs/font-awesome/3.1.0/css/font-awesome.min.css" rel="stylesheet" type="text/css" />

    <script src="/dashboard/javascripts/modernizr.js" type="text/javascript"></script>


    <link href="/dashboard/images/favicon.png" rel="icon" type="image/png" />


  </head>

  <body class="ru ru_faq">
    <div id="fb-root"></div>
    <script>(function(d, s, id) {
      var js, fjs = d.getElementsByTagName(s)[0];
      if (d.getElementById(id)) return;
      js = d.createElement(s); js.id = id;
      js.src = "//connect.facebook.net/en_US/all.js#xfbml=1&appId=277385395761685";
      fjs.parentNode.insertBefore(js, fjs);
    }(document, 'script', 'facebook-jssdk'));</script>
    <header class="header contain-to-grid">
      <nav class="top-bar" data-topbar>
        <ul class="title-area">
          <li class="name">
            <h1><a href="/dashboard/ru/index.html">Apache Friends</a></h1>
          </li>
          <li class="toggle-topbar menu-icon">
            <a href="#">
              <span>Menu</span>
            </a>
          </li>
        </ul>

        <section class="top-bar-section">
          <!-- Left Nav Section -->
          <ul class="left">
              <li class="item active"><a href="/dashboard/ru/faq.html">ЧаВО</a></li>
              <li class="item "><a href="/dashboard/ru/howto.html">HOW-TO Guides</a></li>
              <li class="item "><a target="_blank" href="/dashboard/phpinfo.php">PHPInfo</a></li>
              <li class="item "><a href="/phpmyadmin/">phpMyAdmin</a></li>
          </ul>
        </section>
      </nav>
    </header>

    <div class="wrapper">
      <div class="hero">
  <div class="row">
    <div class="large-12 columns">
      <h1>OS X <span>Часто задаваемые Вопросы</span></h1>
    </div>
  </div>
</div>
<div class="row">
    <div class="large-8 columns">
    <dl class="accordion">
    
      <dt>What is the difference between XAMPP for OS X and XAMPP-VM?</dt>
      <dd>
        <p>
        <ul>
          <li>XAMPP for OS X is a native installer for OS X. It installs Apache, PHP and other XAMPP components directly on your OS X system, in the /Applications/XAMPP folder.</li>
          <li>XAMPP-VM is a virtual machine for OS X. It includes Apache, PHP and other XAMPP components and runs them in a Linux-based virtual machine on your OS X system.</li>
        </ul>
        </p>
        <p>For more information, refer to the blog post at <a href="https://www.apachefriends.org/blog/new_xampp_20170628.html">https://www.apachefriends.org/blog/new_xampp_20170628.html</a>.</p>.
      </dd> 
    
      <dt>Как мне установить XAMPP на Mac OS X?</dt>
      <dd>
      <p>Чтобы установить XAMPP просто сделайте следующее:</p>
      <ul>
        <li>Откройте DMG-образ.</li>
        <li>Дважды кликните по картинке чтобы запустить процесс установки.</li></ul>
      <p>Вот и всё. Теперь XAMPP установлен под каталогом /Applications/XAMPP.</p>
      </dd>
      <dt>XAMPP включает в себя MySQL или MariaDB?</dt>
      <dd>
        <p>С момента выхода XAMPP 5.5.30 и 5.6.14, XAMPP предоставляет MariaDB вместо MySQL. Команды и инструменты для обоих одни и те же.</p>
      </dd>
      <dt>Как я мне запустить XAMPP?</dt>
      <dd>
      <p>Чтобы запустить XAMPP просто откройте XAMPP Control и запустите Apache, MySQL и ProFTPD. Имя для XAMPP Control "manager-osx".</p>      
      </dd>
      <dt>Как я мне остановить XAMPP?</dt>
      <dd>
      <p>Чтобы остановить XAMPP просто откройте XAMPP Control и остановите сервера. Имя для XAMPP Control "manager-osx".</p>      
      </dd>
      <dt>Как мне проверить, что всё сработало?</dt>
      <dd>
      <p>Введите следующую URL в веб браузер:</p>
      <p><code>http://localhost</code></p>

      <p>You should see the XAMPP start page, as shown below.</p>
        <img src="/dashboard/images/screenshots/xampp-macosx-start.jpg" />    
      </dd>
      <dt>Готов ли XAMPP к производственному использованию?</dt>
      <dd><p>XAMPP не предназначен для производственного использования, а только для сред разработки. XAMPP настроен таким образом чтобы быть как можно более открытым и позволять разработчику всё что он/она захочет. Для сред разработки это прекрасно, но в производственной среде это может быть пагубно.</p>
      <p>Вот список отсутствующих мер безопасности в XAMPP:</p>
      <ol>
        <li>MySQL администратор (root) не имеет пароля.</li>
        <li>MySQL сервер доступен через сеть.</li>
        <li>ProFTPD uses the password "lampp" for user "daemon".</li>
      </ol>
      <p>Чтобы исправить большинство пробелов в безопасности просто вызовите следующую команду:</p>
      <p><code>sudo /Applications/XAMPP/xamppfiles/xampp security</code></p>
      <p>Это запустит небольшую проверку безопасности чтобы сделать установку XAMPP безопасной.</p></dd>

      <dt>Что означают сообщения об ошибках которые я вижу когда запускаю XAMPP?</dt>
      <dd>
        <p>Вы можете получить несколько сообщений об ощибках когда запускаете XAMPP:</p>
        <p><code>LAMPP-Apache is already running.<br />
            An Apache daemon is already running.</code></p>
        <p>Скрипт запуска LAMPP не смог запустить XAMPP-Apache потому что экземпляр Apache уже запущен. Чтобы запустить XAMPP правильно, сначала вам нужно остановить этот процесс.</p>
        <p><code>LAMPP-MySQL is already running.<br />
            A MySQL daemon is already running.</code></p>
        <p>В основном это по тем же причинам что и выше указанная ошибка. Скрипт запуска LAMPP нащёл процесс уже запущенный на вашей системе. Чтобы запустить LAMPP правильно, сначала вам нужно остановить этот процесс.</p>
      </dd>

      <dt>Apache doesn't seem to start. What can I do?</dt>
      <dd>
        <p>Эта ошибка может иметь место по нескольким причинам. Apache показывает эту ощибку при разных условиях. Чтобы найти конкретную причину нам нужно будет немного поисследовать:</p>
        <p><code>tail -2 /Applications/XAMPP/logs/error_log</code></p>
        <p>Если вы получили сообщение об ошибке посетите <a href="/community.html">страницы нашего сообщества</a> для помощи.</p>
      </dd>

      <dt>Как мне сделать более безопасной мою установку XAMPP?</dt>
      <dd>
        <p>В установке по умолчанию, в XAMPP не установлено паролей и запускать XAMPP с такой конфигурацией доступной другим, не рекомендуется.</p>
        <p>Просто введите следующую команду (как root) чтобы начать простую проверку безопасности:</p>
        <p><code>sudo /Applications/XAMPP/xamppfiles/xampp security</code></p>
        <p>Теперь вы должны видить следующий диалог на вашем экране:</p>
        <p><code>
XAMPP: Quick security check...</br>
XAMPP: MySQL is accessable via network.</br>
XAMPP: Normaly that's not recommended. Do you want me to turn it off? [yes] yes</br>
XAMPP: Turned off.</br>
XAMPP: Stopping MySQL...</br>
XAMPP: Starting MySQL...</br>
XAMPP: The MySQL/phpMyAdmin user pma has no password set!!!</br>
XAMPP: Do you want to set a password? [yes] yes</br>
XAMPP: Password: ******</br>
XAMPP: Password (again): ******</br>
XAMPP: Setting new MySQL pma password.</br>
XAMPP: Setting phpMyAdmin's pma password to the new one.</br>
XAMPP: MySQL has no root passwort set!!!</br>
XAMPP: Do you want to set a password? [yes] yes</br>
XAMPP: Write the passworde somewhere down to make sure you won't forget it!!!</br>
XAMPP: Password: ******</br>
XAMPP: Password (again): ******</br>
XAMPP: Setting new MySQL root password.</br>
XAMPP: Setting phpMyAdmin's root password to the new one.</br>
XAMPP: The FTP password for user 'nobody' is still set to 'lampp'.</br>
XAMPP: Do you want to change the password? [yes] yes</br>
XAMPP: Password: ******</br>
XAMPP: Password (again): ******</br>
XAMPP: Reload ProFTPD...</br>
XAMPP: Done.</br>
  </code></p>
        <p>(1) Установка пароля защитит демонстративные страницы XAMPP (http://localhost/xampp/) использующие этот пароль. Имя пользователя 'lampp'!</p>
        <p>После вызова этой команды ваша установка XAMPP должна быть более безопасной.</p>
      </dd>

      <dt>Как мне активировать OCI8/Oracle расширение для PHP?</dt>
      <dd>
        <p>Чтобы активировать OCI8/Oracle расширение для PHP пожалуйста выполните следующую команду:</p>
        <p><code>sudo /Applications/XAMPP/xamppfiles/lampp oci8</code></p>
        <p>Появится следующий диалог:</p>
        <p><code>Please enter the path to your Oracle or Instant Client installation:</br>
[/Applications/XAMPP/xamppfiles/lib/instantclient-********.0] </br>
installing symlinks...</br>
patching php.ini...</br>
OCI8 add-on activation likely successful.</br>
LAMPP: Stopping Apache with SSL...</br>
LAMPP: Starting Apache with SSL...</code></p>
        <p>Теперь расширение должо быть активировано.</p>
      </dd>

      <dt>Как мне включить доступ к phpMyAdmin извне?</dt>
      <dd>
        <p>В базовой конфигурации XAMPP phpMyAdmin доступен только с того же хоста, на котором запущен XAMPP, по адресу <a href="http://127.0.0.1">http://127.0.0.1</a> или <a href="http://localhost">http://localhost</a>.</p>
        <p>ВАЖНО: Включение внешнего доступа для phpMyAdmin в производственных средах представляет собой значительный риск для безопасности. Вам настоятельно рекомендуется разрешать доступ только с localhost. Удаленный злоумышленник может воспользоваться любой существующей уязвимостью для выполнения кода или изменения ваших данных.</p>
        <p>Чтобы включить удалённый доступ к phpMyAdmin, следуйте шагам ниже:</p>
        <ul>
          <li>Edit the xamppfiles/etc/extra/httpd-xampp.conf file in your XAMPP installation directory.</li>
          <li>В этом файле, найдите строки, указанные снизу. 
            <p><code>
                Alias /phpmyadmin "/Applications/XAMPP/xamppfiles/phpmyadmin"
                &lt;Directory "/Applications/XAMPP/xamppfiles/phpmyadmin"&gt;
                  AllowOverride AuthConfig
                  Require local
            </code></p>
          </li>
          <li>Затем, замените 'Require local' на 'Require all granted'.</li>
            <p><code>
                Alias /phpmyadmin "/Applications/XAMPP/xamppfiles/phpmyadmin"
                &lt;Directory "/Applications/XAMPP/xamppfiles/phpmyadmin"&gt;
                  AllowOverride AuthConfig
                  Require all granted
            </code></p>
          <li>Перезапустите сервер Apache, используя панель управления XAMPP.</li>
        </ul>
      </dd>

      <dt>Where are the main XAMPP configuration files?</dt>
      <dd>
        <p>The main XAMPP configuration files are located as follows:</p>
        <ul>
          <li>Файлы конфигурации Apache: /Applications/XAMPP/xamppfiles/etc/httpd.conf, /Applications/XAMPP/xamppfiles/etc/extra/httpd-xampp.conf</li>
          <li>PHP configuration file: /Applications/XAMPP/xamppfiles/etc/php.ini</li>
          <li>MySQL configuration file: /Applications/XAMPP/xamppfiles/etc/my.cnf</li>
          <li>ProFTPD configuration file: /Applications/XAMPP/xamppfiles/etc/proftpd.conf</li>
        </ul>
      </dd>

      <dt>How do I send email with XAMPP?</dt>
      <dd>
        <p>To send email with XAMPP, use the PEAR Mail and Net_SMTP packages, which allow you to send email using an external SMTP account (such as a Gmail account). Follow these steps:</p>
        <ul>
          <li>Install the Mail and Net_SMTP PEAR modules:
          <code>
          pear install Net_SMTP Mail
          </code>
          Note that if these packages are already installed in your system you see the messages below when executing that command:
          <code>
          Ignoring installed package pear/Net_SMTP
          Ignoring installed package pear/Mail
          Nothing to install
          </code>
          </li>
          <li>
          Create the following example script in your "htdocs" directory to send an email:
          <code>
          &lt;?php
          require_once "Mail.php";

          $from = "<EMAIL>";
          $to = '<EMAIL>';

          $host = "ssl://smtp.gmail.com";
          $port = "465";
          $username = '<EMAIL>';
          $password = 'your-gmail-password';

          $subject = "test";
          $body = "test";

          $headers = array ('From' => $from, 'To' => $to,'Subject' => $subject);
          $smtp = Mail::factory('smtp',
             array ('host' => $host,
               'port' => $port,
               'auth' => true,
               'username' => $username,
               'password' => $password));

          $mail = $smtp->send($to, $headers, $body);

          if (PEAR::isError($mail)) {
            echo($mail->getMessage());
          } else {
            echo("Message successfully sent!\n");
          }
          ?>
          </code>
          <p>Remember to replace the dummy values shown with your actual Gmail address and account password. If you don't plan to use Gmail's SMTP server, replace the SMTP host details with appropriate values for your organization or ISP's SMTP server.</p>
          </li>
          <li>
          Execute the script by browsing to it using your Web browser. You should see a notification that the message was successfully sent, and the message should be delivered to the recipient email address.
          </li>
        </ul>
      </dd>
      
      <dt>Как мне сделать резервное копирование или востановить мою XAMPP систему?</dt>
      <dd>
        <p><strong>Предупреждение:</strong> Функции резервного копирования и востановления ещё разрабатываются и могут работать неправильно.</p>
        <p>Вы можете создать резервное копирование с помощью:</p>
        <p><code>sudo /Applications/XAMPP/xamppfiles/xampp backup</code></p>
        <p>или</p>
        <p><code>sudo /Applications/XAMPP/xamppfiles/xampp backup secret</code></p>
        <p>Где "secret" это ваш MySQL root пароль. Эта команда выведет следующее:</p>
        <p><code>Backing up databases...</br>
Backing up configuration, log and htdocs files...</br>
Calculating checksums...</br>
Building final backup file...</br>
Backup finished.</br>
Take care of /Applications/XAMPP/xamppfiles/backup/xampp-backup-22-01-14.sh</code></p>

        <p>Файл /Applications/XAMPP/xamppfiles/backup/xampp-backup-22-01-14.sh (в выше указанном примере) содержит резервную копию ваших данных. Переместите этот файл в безопасное место.</p>

        <p>На новой машине вам нужна такая же версия XAMPP, как на вашей изначальной/исходной машине.</p>
        <p><code>sudo sh xampp-backup-22-01-14.sh</code></p>
        <p>Вы должны видеть что-то вроде этого:</p>
        <p><code>Checking integrity of files...</br>
Restoring configuration, log and htdocs files...</br>
Checking versions...</br>
Installed: XAMPP 1.4.2</br>
Backup from: XAMPP 1.4.2</br>
Restoring MySQL databases...</br>
Restoring MySQL user databases...</br>
Backup complete. Have fun!</br>
You may need to restart XAMPP to complete the restore.</br>
  </code></p>
        <p>Вот и всё. Помните это ещё не зрелая (beta) функция.</p>
      </dd>
    </dl>

  </div>
</div>

    </div>

    <footer class="footer row">
      <div class="columns">
        <div class="footer_lists-container row collapse">
          <div class="footer_social columns large-2">
            <ul class="social">
  <li class="twitter"><a href="https://twitter.com/apachefriends">Follow us on Twitter</a></li>
  <li class="facebook"><a href="https://www.facebook.com/we.are.xampp">Like us on Facebook</a></li>
</ul>

            <p class="footer_copyright">Copyright (c) 2022, Apache Friends</p>
          </div>
          <ul class="footer_links columns large-9">
            <li><a href="https://www.apachefriends.org/blog.html">Блог</a></li>
            <li><a href="/privacy_policy.html">Политика приватности</a></li>
            <li>
<a target="_blank" href="http://www.fastly.com/">                CDN предусмотрено
                <img width="48" data-2x="/dashboard/images/<EMAIL>" src="/dashboard/images/fastly-logo.png" />
</a>            </li>
          </ul>
        </div>
      </div>
    </footer>

    <!-- JS Libraries -->
    <script src="//code.jquery.com/jquery-1.10.2.min.js"></script>
    <script src="/dashboard/javascripts/all.js" type="text/javascript"></script>
  </body>
</html>
