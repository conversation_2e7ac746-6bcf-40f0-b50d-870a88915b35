<?php
    function greeting() {
        date_default_timezone_set("America/New_York");
        $hour = date("H");
        if ($hour < "12") {
            echo "Good Morning! <br>";
            //echo "<img src = '../Images/morning.png' width = 200px height = 200px>";
            echo "<img src = 'morning.png' width = 200px height = 200px>";
        } else if ($hour < "18") {
            echo "Good Afternoon! <br>";
            //echo "<img src = '../Images/afternoon.png' width = 200px height = 200px>";
            echo "<img src = 'afternoon.png' width = 200px height = 200px>";
        } else {
            echo "Good Evening! <br>";
            //echo "<img src = '../Images/evening.png' width = 200px height = 200px>";
            echo "<img src = 'evening.png' width = 200px height = 200px>";
        }
    }

    function carValue($price, $numYears) {
        if ($numYears >= 40)
            $value = 1.5 * $price;
        else if ($numYears >= 20)
            $value = 0.2 * $price;
        else if ($numYears >= 10)
            $value = 0.4 * $price;
        else
            $value = 0.8 * $price;
        return $value;
    }

    function valueTable($rate, $n) {
        echo "<table border = '1'>";
            echo "<tr>";
                for($i = 1; $i <= $n; $i++) {
                    echo "<th> $i </th>";
                }
            echo "</tr>";

            global $myCarValue;
            $newValue = $myCarValue;

            echo "<tr>";
                for($i = 1; $i <= $n; $i++) {
                    $newValue = $newValue * (1 - $rate);
                    echo "<td> $ " .number_format($newValue, 2). " </td>";
                }
            echo "</tr>";
        echo "</table>";
    }
?>