<html>
    <head>
        <style>
            .error {
                color: #FF0000;
            }
        </style>
    </head>

    <body>
        <?php
            function get_input($what, $from) {
                if (array_key_exists($what, $from))
                    return $from[$what];
                else
                    return null;
            }

            function clean_input($input) {
                if ($input == null)
                    return null;

                $input = trim($input);
                $input = htmlspecialchars($input);
                return $input;
            }

            $name = $address = $email = $howMany = $favFruit = $brochure = "";
            $nameMSG = $emailMSG = "";

            if (get_input("submit", $_POST) != null) {
                $name = clean_input(get_input("name", $_POST));
                $address = clean_input(get_input("address", $_POST));
                $email = clean_input(get_input("email", $_POST));
                $howMany = clean_input(get_input("howMany", $_POST));
                $favFruit = clean_input(get_input("favFruit", $_POST));
                $brochure = clean_input(get_input("brochure", $_POST));

                if (empty($name))
                    $nameMSG = "Name is required!";
                if (empty($email))
                    $emailMSG = "Email is required!";
                else {
                    if (!filter_var($email, FILTER_VALIDATE_EMAIL))
                        $emailMSG = "Email format is not valid!";
                }
            }
        ?>

        <h6 align=center>Created by Dr. Lai</h6>
        <h1 align=center>Student Fruit Survey</h1>
        <hr/>

        <form method="post" action="<?php echo $_SERVER['PHP_SELF']; ?>">
            <table style="background-color:pink;width:50%;margin:auto;">
                <tr>
                    <td align=right>Name</td>
                    <td> <input type="text" name="name" value="<?php echo $name; ?>"> <span class='error'>*<?php echo $nameMSG; ?></span> </td>
                </tr>

                <tr>
                    <td align=right>Address</td>
                    <td> <input type="text" name="address" value="<?php echo $address; ?>"> </td>
                </tr>

                <tr>
                    <td align=right>Email</td>
                    <td> <input type="text" name="email" value="<?php echo $email; ?>"> <span class='error'>*<?php echo $emailMSG; ?></span> </td>
                </tr>

                <tr>
                    <td align=right >How many pieces of fruit do you eat per day? </td>
                    <td><input type="radio" name="howMany" value="zero" <?php if($howMany == "zero") echo "checked"; ?> > 0
                        <input type="radio" name="howMany" value="one"  <?php if($howMany == "one") echo "checked"; ?>  > 1
                        <input type="radio" name="howMany" value="two"  <?php if($howMany == "two") echo "checked"; ?>  > 2
                        <input type="radio" name="howMany" value="twoplus"  <?php if($howMany == "twoplus") echo "checked"; ?>  > More than 2
                    </td>
                </tr>

                <tr>
                    <td align=right>Your favorite fruit is:</td>
                    <td>
                        <select name="favFruit" >
                            <option value="apple"       <?php if($favFruit == "apple") echo "selected"; ?>  >Apple</option>
                            <option value="banana"      <?php if($favFruit == "banana") echo "selected"; ?> >Banana</option>
                            <option value="plum"        <?php if($favFruit == "plum") echo "selected"; ?>   >Plum</option>
                            <option value="pomegranate" <?php if($favFruit == "pomegranate") echo "selected"; ?> >Pomegranate</option>
                            <option value="strawberry"  <?php if($favFruit == "strawberry") echo "selected"; ?> >Strawberry</option>
                            <option value="watermelon"  <?php if($favFruit == "watermelon") echo "selected"; ?> >Watermelon</option>
                            <option value="other"       <?php if($favFruit == "other") echo "selected"; ?>  >other</option>
                        </select>
                    </td>
                </tr>

                <tr>
                    <td align=right>Would you like a brochure? </td>
                    <td> <input type="checkbox" name="brochure" value="Yes" <?php if($brochure == "Yes") echo "checked"; ?> > </td>
                </tr>

                <tr>
                    <td></td>
                    <td><input type="submit" name="submit" value="Submit"> </td>
                </tr>
            </table>
        </form>
        <hr/>

        <?php
            if  (get_input("submit", $_POST) != null) {
                if (empty($name) || empty($email) || empty($howMany) || empty($favFruit)) {
                    echo "Missing information is required. Please fill in before submit!";
                } else {
                    echo "Your name is ".$name."<br>";
                    echo "Your address is ".$address."<br>";
                    echo "Your email is ".$email."<br>";

                    if($howMany == "zero") {
                        echo "You eat zero pieces of fruit each day.<br>";
                    } elseif($howMany == "one") {
                        echo "You eat one pieces of fruit each day.<br>";
                    } elseif($howMany == "two") {
                        echo "You eat two pieces of fruit each day.<br>";
                    } elseif($howMany == "twoplus") {
                        echo "You eat more than 2 pieces of fruit each day.<br>";
                    }

                    echo "Your favorite fruit is ".$favFruit.".<br>";

                    if($brochure == "Yes") {
                        echo "You would like a brochure.<br>";
                    } else {
                        echo "You would not like a brochure.<br>";
                    }
                }
            }
        ?>
    </body>
</html>