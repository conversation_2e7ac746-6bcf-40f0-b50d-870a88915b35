<html>
    <head>
        <title>Lab 4</title>
    </head>

    <body>
        <h1>Calory Calculator</h1>

        <?php
            $calorieIntake = array("Breakfast"=>"400", "Lunch"=>"600", "Dinner"=>"1000", "Snacks"=>"200");
            $caloriesBurned = array("Walking"=>"100", "Gardening"=>"400", "Cooking"=>"200", "Running"=>"1000", "Weight Lifting"=>"1000");

            $totalIntake = 0;
            $totalBurned = 0;

            echo "<b>The calories you intook today:</b>";
            echo "<table>";
            if(isset($_POST["intake"]) && !empty($_POST["intake"])) {
                foreach($_POST["intake"] as $meal) {
                    echo "<tr>";
                    echo "<td>$meal</td>";
                    echo "<td>$calorieIntake[$meal]</td>";
                    echo "</tr>";
                    $totalIntake += $calorieIntake[$meal];
                }
            }
            echo "<tr> <td><b>Total</b></td> <td><b>$totalIntake</b></td> </tr>";
            echo "</table>";
            echo "<br>";

            echo "<b>The calories you burned today:</b>";
            echo "<table>";
            if(isset($_POST["burned"]) && !empty($_POST["burned"])) {
                foreach($_POST["burned"] as $activity) {
                    echo "<tr>";
                    echo "<td>$activity</td>";
                    echo "<td>$caloriesBurned[$activity]</td>";
                    echo "</tr>";
                    $totalBurned += $caloriesBurned[$activity];
                }
            }
            echo "<tr> <td><b>Total</b></td> <td><b>$totalBurned</b></td> </tr>";
            echo "</table><br>";
            echo "<br>";

            $difference = $totalBurned - $totalIntake;

            if($difference > 0) {
                echo "You burned $difference more calories than you took in.";
                echo "<br><br> <img src='../Images/happy.png' width='100'>";
            } elseif($difference < 0) {
                $absDifference = abs($difference);
                echo "You took in $absDifference more calories than you burned.";
                echo "<br><br> <img src='../Images/sad.png' width='100'>";
            } else {
                echo "You took in $totalIntake calories, the same amount you burned.";
                echo "<br><br> <img src='../Images/neutral.png' width='100'>";
            }
        ?>
    </body>
</html>