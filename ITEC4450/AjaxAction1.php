<?php
    // fake stock price using a random number
    //echo "Apple is: $" .rand(125, 165);

    // in real time
    require "ITEC4550curl.php";

    //$url = "https://quote.cnbc.com/quote-html-webservice/restQuote/symbolType/symbol?symbols=TSLA";
    $url = "https://quote.cnbc.com/quote-html-webservice/restQuote/symbolType/symbol?symbols=NVDA";
    $info = web_get_contents($url);

    $res = json_decode($info);

    echo $res->FormattedQuoteResult->FormattedQuote[0]->onAirName." is $".$res->FormattedQuoteResult->FormattedQuote[0]->last;
?>