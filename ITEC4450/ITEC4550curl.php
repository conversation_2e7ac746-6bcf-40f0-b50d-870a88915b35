<?php

function cmd_get_contents($url)
{
    if(empty($url) || !isset($url))
        return false;
        
    $cmd = 'curl -A "a" "'.$url.'"';

    $res = shell_exec($cmd);
    if($res === false)
        return false;
    if($res === null)
        return false;    
        
    return $res;
}

function web_get_contents($url, $cookie=null)
{
    if(empty($url) || !isset($url))
        return false;
    if(empty($cookie) || !isset($cookie))
    { }                
    
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_CUSTOMREQUEST, 'GET');
    curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, FALSE);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, FALSE);
    curl_setopt($ch, CURLOPT_HTTPHEADER, array('Content-Type: application/json'));
    curl_setopt($ch, CURLOPT_COOKIE, $cookie);
    curl_setopt($ch, CURLOPT_USERAGENT, "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0");
    curl_setopt($ch, CURLOPT_REFERER, 'https://www.domain.com/');

    if(FALSE === ($retval = curl_exec($ch))) 
    {   return false;    } 
    else 
    {
        curl_close($ch);
        return $retval;
    }
}

//$url = "https://www.google.com/";
//$url = "https://www.google.com/search?q=ggc";

//$url = "https://quote.cnbc.com/quote-html-webservice/restQuote/symbolType/symbol?symbols=AAPL";
//$info = http_get_contents($url, "");
//echo "result: <br/>".$info;
?>