<html>
    <head>
        <title>6-17 One</title>
    </head>

    <body>
        <h1>Please sign up</h1>
        <form method="post" action="<?php echo $_SERVER['PHP_SELF']; ?>">
            Name: <input type="text" name="name"> <br>
            Email: <input type="text" name="email"> <br>
            Major: <input type="text" name="major"> <br>
            <input type="submit" name="submit" value="submit">
            <input type="reset" name="reset" value="reset">
        </form>

        <?php
            if (isset($_POST["submit"])) {
                echo "<hr>";
                $file = "SignUpInfo.txt";

                if (!file_exists($file))
                    $infoStr = "";
                else {
                    $infoStr = file_get_contents($file);
                    $infoStr = trim($infoStr);
                    $infoList = explode("\n", $infoStr);
                    foreach ($infoList as $index=> $line) {
                        $personInfo[$index] = explode("\t", $line); // $personInfo is a 2D array
                    }
                    // verify if the email is used
                    foreach ($personInfo as $person) {
                        if ($person[1] == $_POST["email"]) {
                            echo "The email is already used. Choose another to try again!";
                            exit();
                        }
                    }
                }

                echo "Thank you for signing up. <br>";
                echo "Your name: ".$_POST["name"]."<br>";
                echo "Your email: ".$_POST["email"]."<br>";
                echo "Your major: ".$_POST["major"]."<br>";

                $info = "";
                $info .= $_POST["name"];
                $info .= "\t";
                $info .= $_POST["email"];
                $info .= "\t";
                $info .= $_POST["major"];
                $info .= "\t";
                $info .= $_SERVER["REMOTE_ADDR"];
                $info .= "\t";

                date_default_timezone_set("America/New_York");

                $info .= date("m/d/Y");
                $info .= "\t";
                $info .= date("h:i:sA");
                $info .= "\t";
                $info .= rand(100000, 999999);
                $info .= "\n";

                file_put_contents($file, $info, FILE_APPEND|LOCK_EX);
            }
        ?>
    </body>
</html>