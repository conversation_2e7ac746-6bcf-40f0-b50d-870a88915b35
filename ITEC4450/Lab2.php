<html>
    <head>
        <title>Lab 2</title>
        <style>
            body {
                text-align: center;
            }
        </style>
    </head>

    <body>
        <h1>Web Bank Interest Rate</h1>

        <table border="1" align="center">
            <tr>
                <td>Amount</td>
                <td>Checking</td>
                <td>Savings</td>
            </tr>

            <tr>
                <td> < $10,000 </td>
                <td> 0.1% </td>
                <td> 1 %</td>
            </tr>

            <tr>
                <td> >= $10,000</td>
                <td> 0.2% </td>
                <td> 1.5% </td>
            </tr>
        </table>

        <h2 style="color: green">My Calculator</h2>

        <?php
            $deposit = rand(1, 100000);
            $years = rand(1, 10);
            $accountType = (rand(0, 1) == 0) ? "checking" : "savings";

            function calculateBalance($deposit, $years, $accountType) {
                if ($accountType == "checking") {
                    $interestRate = ($deposit < 10000) ? 0.001 : 0.002;
                } else { // savings
                    $interestRate = ($deposit < 10000) ? 0.01 : 0.015;
                }

                $totalBalance = $deposit * pow((1 + $interestRate), $years);
                $totalInterest = $totalBalance - $deposit;

                return ["balance" => $totalBalance, "interest" => $totalInterest];
            }

            $result = calculateBalance($deposit, $years, $accountType);
            $totalBalance = $result["balance"];
            $totalInterest = $result["interest"];

            echo "If I deposit <span style = 'color: red'> $" .number_format($deposit, 2). "</span>
                 in a <span style = 'color: red'>" .$accountType. "</span> account,
                 after <span style = 'color: red'>" .$years. "</span> years my balance is:";

            echo "<h2 style = 'color: green'> $" .number_format($totalBalance, 2). "</h2>";
            echo "My total interest is <b><span style='color: green'> $" .number_format($totalInterest, 2). "</span></b>.";
            echo "<br><br>";

            echo "<img src = 'money.png' width = '300px' height = '300px' style = 'display: block; margin: 0 auto;'>";

            echo "<br><br>";
            echo "Deposit Amount: <b><span style = 'color: green'> $" .number_format($deposit, 2). "</span></b>";

            function balanceTable($deposit) {
                $checkingRate = ($deposit < 10000) ? 0.001 : 0.002;
                $savingsRate = ($deposit < 10000) ? 0.01 : 0.015;

                echo "<table border='1' align='center'>";
                    echo "<tr>";
                        echo "<th>Type</th>";
                            for($i = 1; $i <= 10; $i++) {
                                echo "<th>Year $i</th>";
                            }
                    echo "</tr>";

                    echo "<tr>";
                        echo "<td>Checking</td>";
                            $checkingBalance = $deposit;
                            for($i = 1; $i <= 10; $i++) {
                                $checkingBalance *= (1 + $checkingRate);
                                echo "<td>$" .number_format($checkingBalance, 2). "</td>";
                            }
                    echo "</tr>";

                    echo "<tr>";
                        echo "<td>Savings</td>";
                            $savingsBalance = $deposit;
                            for($i = 1; $i <= 10; $i++) {
                                $savingsBalance *= (1 + $savingsRate);
                                echo "<td> $" .number_format($savingsBalance, 2). "</td>";
                            }
                    echo "</tr>";
                echo "</table>";
            }
            balanceTable($deposit);

            echo "<br><br>";
        ?>
    </body>
</html>