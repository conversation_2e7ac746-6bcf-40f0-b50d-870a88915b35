<html>
    <head>
        <title>6-17</title>
    </head>

    <body>
        <?php
            $file = "Students-Grades-Info.txt";
            $studentStr = file_get_contents($file);
            $studentStr = trim($studentStr);

            $studentList = explode("\n", $studentStr); // the result is a 1D array
            foreach ($studentList as $index=>$student) {
                $studentInfo[$index] = explode("\t", $student); // the result $studentInfo is a 2D array
            }

            // table 1
            echo "<table border='1'>";
            echo "<tr>
                    <th>#</th>
                    <th>Name</th>
                    <th>Email</th>
                    <th>Major</th>
                    <th>Grade</th>
                    <th>IP</th>
            </tr>";
            foreach ($studentInfo as $index=>$student) {
                echo "<tr>";
                echo "<td>".($index + 1)."</td>";
                foreach ($student as $info)
                    echo "<td>".$info."</td>";
                echo "</tr>";
            }
            echo "</table>";
            echo "Totally ".count($studentInfo)." rows.<br>";
        ?>

        <?php
            echo "<hr>";

            $major = "Security";
            $nFound = 0;

            foreach ($studentInfo as $student) {
                if ($major == $student[2])
                    $nFound++;
            }
            echo "There are totally ".$nFound." students in the ".$major." major.<br>";
        ?>

        <?php
            echo "<hr>";
            echo "Find all students of Digital Media major and having 100 points:";

            $major = "Digital Media";
            $nFound = 0;

            // table 2
            echo "<table border='1'>";
            echo "<tr>
                    <th>#</th>
                    <th>Name</th>
                    <th>Email</th>
                    <th>Major</th>
                    <th>Grade</th>
                    <th>IP</th>
            </tr>";
            foreach ($studentInfo as $index=>$student) {
                if ($major == $student[2] && intval($student[3]) == 100) {
                    $nFound++;
                    echo "<tr>";
                    echo "<td>".($index + 1)."</td>";
                    foreach ($student as $info)
                        echo "<td>".$info."</td>";
                    echo "</tr>";
                }
            }
            echo "</table>";
            echo "There are totally ".$nFound." students in the ".$major." major with 100 points.<br>"; // total is 39
        ?>

        <?php
            echo "<hr>";
            echo "Find all students of Software major and having 0 points:";

            $major = "Software";
            $nFound = 0;

            // table 3
            echo "<table border='1'>";
            echo "<tr>
                    <th>#</th>
                    <th>Name</th>
                    <th>Email</th>
                    <th>Major</th>
                    <th>Grade</th>
                    <th>IP</th>
            </tr>";
            foreach ($studentInfo as $index=>$student) {
                if ($major == $student[2] && intval($student[3]) == 0) {
                    $nFound++;
                    echo "<tr>";
                    echo "<td>".($index + 1)."</td>";
                    foreach ($student as $info)
                        echo "<td>".$info."</td>";
                    echo "</tr>";
                }
            }
            echo "</table>";
            echo "There are totally ".$nFound." students in the ".$major." major with 0 points.<br>";
        ?>

        <?php
            echo "<hr>";

            $allMajor = array("Digital Media", "Security", "Software", "Business", "Other");
            $allFound = array(0, 0, 0, 0, 0);

            foreach ($studentInfo as $student) {
                foreach ($allMajor as $i=>$major) {
                    if ($major == $student[2])
                        $allFound[$i] ++;
                }
            }

            echo "<table>";
            echo "<tr>
                    <th>Major</th>
                    <th># of Students</th>
                    <th>Bar</th>
                    <th>Percentage</th>
            </tr>";
            foreach ($allMajor as $i=>$major) {
                $p = $allFound[$i]/count($studentInfo)*100;
                $p = round($p, 2);
                echo "<tr>";
                echo "<td>".$major."</td>";
                echo "<td>".$allFound[$i]."</td>";
                echo "<td> <progress min=0 max=100 value=".$p."></progress> </td>";
                echo "<td style='color: red'>".$p."%</td>";
                echo "</tr>";
            }
            echo "</table>";
        ?>
    </body>
</html>