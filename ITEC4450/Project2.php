<!DOCTYPE html>
<html>
    <head>
        <title>Project for keeping all your input</title>
        <style>
            .required {
                color: red;
            }
        </style>
    </head>

    <body>
        <h1>Welcome to this Web Based Test!!!</h1>
        <p>Please answer the following questions:</p>
        <hr/>

        <?php
            function get_input($what, $from) {
                if (array_key_exists($what, $from))
                    return $from[$what];
                else
                    return null;
            }

            function clean_input($input) {
                if ($input == null)
                    return null;
                $input = trim($input);
                $input = htmlspecialchars($input);
                return $input;
            }

            $name = $email = $major = "";
            $nameMSG = $emailMSG = "";
            $Q1 = $Q2 = $Q3 = $Q4 = "";

            if (get_input("submit", $_POST) != null) {
                $name = clean_input(get_input("name", $_POST));
                $email = clean_input(get_input("email", $_POST));
                $major = clean_input(get_input("major", $_POST));
                $Q1 = clean_input(get_input("Q1", $_POST));
                $Q2 = clean_input(get_input("Q2", $_POST));
                $Q3 = clean_input(get_input("Q3", $_POST));
                $Q4 = clean_input(get_input("Q4", $_POST));

                if (empty($name))
                    $nameMSG = "Name is required!";
                if (empty($email))
                    $emailMSG = "Email is required!";
                else {
                    if (!filter_var($email, FILTER_VALIDATE_EMAIL))
                        $emailMSG = "Email format is not valid!";
                }
            }
        ?>

        <form action="<?php echo $_SERVER['PHP_SELF']; ?>" method="post">
            Name:   <input type="text" name="name" value="<?php echo $name; ?>">    <span class="required">* <?php echo $nameMSG; ?> </span> <br/><br/>
            E-mail: <input type="text" name="email" value="<?php echo $email; ?>">  <span class="required">* <?php echo $emailMSG; ?> </span> <br>
            <hr/>

            Choose your major area of study:
            <select name="major">
                <option value="Digital Media" <?php if($major == "Digital Media") echo "selected"; ?> >Digital Media</option>
                <option value="Software" <?php if($major == "Software") echo "selected"; ?> >Software</option>
                <option value="Security" <?php if($major == "Security") echo "selected"; ?> >Security</option>
                <option value="Business" <?php if($major == "Business") echo "selected"; ?> >Business</option>
                <option value="Other" <?php if($major == "Other") echo "selected"; ?> >Other</option>
            </select>
            <hr/>

            <p>Question 1 (25 points)</p>
            <p>What does PHP stand for? </p>
            <input type="radio" value="A" name="Q1" <?php if ($Q1 == "A") echo "checked"; ?> >A. Private Home Page <br/>
            <input type="radio" value="B" name="Q1" <?php if ($Q1 == "B") echo "checked"; ?> >B. PHP: Hypertext Preprocessor <?php showAnswer(); ?> <br/>
            <input type="radio" value="C" name="Q1" <?php if ($Q1 == "C") echo "checked"; ?> >C. Personal Hypertext Processor <br/>
            <input type="radio" value="D" name="Q1" <?php if ($Q1 == "D") echo "checked"; ?> >D. Preprocessor Home Page <br/>
            <hr/>

            <p>Question 2 (25 points)</p>
            <p>Which symbol is used to declare a variable in PHP?</p>
            <input type="radio" value="A" name="Q2" <?php if ($Q2 == "A") echo "checked"; ?> >A. @ <br/>
            <input type="radio" value="B" name="Q2" <?php if ($Q2 == "B") echo "checked"; ?> >B. $ <?php showAnswer(); ?> <br/>
            <input type="radio" value="C" name="Q2" <?php if ($Q2 == "C") echo "checked"; ?> >C. # <br/>
            <input type="radio" value="D" name="Q2" <?php if ($Q2 == "D") echo "checked"; ?> >D. & <br/>
            <hr/>

            <p>Question 3 (25 points)</p>
            <p>What is the correct way to create a function in PHP?</p>
            <input type="radio" value="A" name="Q3" <?php if ($Q3 == "A") echo "checked"; ?> >A. new_function myFunction() <br/>
            <input type="radio" value="B" name="Q3" <?php if ($Q3 == "B") echo "checked"; ?> >B. function myFunction() <?php showAnswer(); ?> <br/>
            <input type="radio" value="C" name="Q3" <?php if ($Q3 == "C") echo "checked"; ?> >C. create myFunction() <br/>
            <input type="radio" value="D" name="Q3" <?php if ($Q3 == "D") echo "checked"; ?> >D. define myFunction() <br/>
            <hr/>

            <p>Question 4 (25 points)</p>
            <p>Which PHP superglobal is used to collect form data sent with the POST method?</p>
            <input type="radio" value="A" name="Q4" <?php if ($Q4 == "A") echo "checked"; ?> >A. $_GET <br/>
            <input type="radio" value="B" name="Q4" <?php if ($Q4 == "B") echo "checked"; ?> >B. $_POST <?php showAnswer(); ?> <br/>
            <input type="radio" value="C" name="Q4" <?php if ($Q4 == "C") echo "checked"; ?> >C. $_REQUEST <br/>
            <input type="radio" value="D" name="Q4" <?php if ($Q4 == "D") echo "checked"; ?> >D. $_PHP <br/>
            <hr/>

            <input type="checkbox" name="showanswer" value="YES" <?php if (clean_input(get_input("showanswer", $_POST)) == "YES") echo "checked"; ?> > Show correct answers after submission.
            <br/><br/>
            <input type="submit" value="Submit this test" name="submit">
            <input type="submit" name="reset" value="Reset">
        </form>

        <!--for admin -->
        <hr/>
        <div style="text-align:left;background-color:pink;width:50%;margin:auto;">
            <form action="<?php echo $_SERVER['PHP_SELF']; ?>" method="post">
                <div style="text-align:right;margin-right: 20%;">
                    Admistrator ID: <input type="text" name="name"> <span class="required">*</span> <br>
                    Password:       <input type="password" name="passwd"> <span class="required">*</span> <br>
                </div>
                <hr/>

                <div style="text-align:left;margin-left: 20%;">
                    <label><input type="radio" name="showwhat" value="all">     Show all grades</label><br/>
                    <label><input type="radio" name="showwhat" value="p100">    Show all grades that are 100</label><br/>
                    <label><input type="radio" name="showwhat" value="dm0">     Show all grades that are 0 and are of Digital Media Major</label><br/>
                    <label><input type="radio" name="showwhat" value="bydate">  Show all tests that were taken after 8:00am of yesterday</label><br/>
                    <label><input type="radio" name="showwhat" value="byname">  Find student(s)'s grade by name:</label> <input type="text" name="student" value=""><br/>
                </div>
                <hr/>

                <div style="text-align:center;">
                    <input type="submit" value="See results" name="submitme">
                    <input type="reset">
                </div>
            </form>
        </div>
        <hr/>

        <?php
            function showAnswer() {
                if (clean_input(get_input("showanswer", $_POST)) == "YES")
                    echo "<span style='color:red;'> <---This is the correct answer!</span>";
            }

            if (get_input("submit", $_POST) != null) {
                if (empty($name) || empty($email) || !filter_var($email, FILTER_VALIDATE_EMAIL)) {
                    echo "Missing or invalid information is required. Please fill in correctly before submit!";
                } else {
                    echo "Your Test Results: <br>";
                    echo "Name: ".$name."<br>";
                    echo "Email: ".$email."<br>";
                    echo "Major: ".$major."<br>";

                    $score = 0;
                    $nq = 4;
                    $ppq = 100/$nq;

                    if ($Q1 == "B") $score += $ppq; // PHP: Hypertext Preprocessor
                    if ($Q2 == "B") $score += $ppq; // $ symbol
                    if ($Q3 == "B") $score += $ppq; // function myFunction()
                    if ($Q4 == "B") $score += $ppq; // $_POST

                    echo "Your grade for this test is: ".round($score, 1)."%<br>";

                    $file = "test_results.txt";
                    $info = "";
                    $info .= $name;
                    $info .= "\t";
                    $info .= $email;
                    $info .= "\t";
                    $info .= $major;
                    $info .= "\t";
                    $info .= round($score, 1);
                    $info .= "\t";
                    
                    date_default_timezone_set("America/New_York");
                    $info .= date("m/d/Y");
                    $info .= "\t";
                    $info .= date("h:i:sA");
                    $info .= "\n";
                    
                    file_put_contents($file, $info, FILE_APPEND|LOCK_EX);
                }
            }

            if (get_input("submitme", $_POST) != null) {
                $admin_name = clean_input(get_input("name", $_POST));
                $admin_passwd = clean_input(get_input("passwd", $_POST));
                
                if ($admin_name == "admin" && $admin_passwd == "admin") {
                    $file = "test_results.txt";
                    if (file_exists($file)) {
                        $infoStr = file_get_contents($file);
                        $infoStr = trim($infoStr);
                        $infoList = explode("\n", $infoStr);
                        $personInfo = array();
                        
                        foreach ($infoList as $index => $line) {
                            $personInfo[$index] = explode("\t", $line);
                        }
                        
                        $showwhat = clean_input(get_input("showwhat", $_POST));
                        $student_name = clean_input(get_input("student", $_POST));
                        
                        echo "<h3>Test Results</h3>";
                        echo "<table border='1'>";
                        echo "<tr>
                            <th>No.</th>
                            <th>Name</th>
                            <th>Email</th>
                            <th>Major</th>
                            <th>Grade</th>
                            <th>IP Address</th>
                            <th>Date</th>
                            <th>Time</th>
                        </tr>";
                        
                        $found = 0;
                        foreach ($personInfo as $index => $person) {
                            $display = false;
                            
                            if ($showwhat == "all") {
                                echo "The grade for each student is shown as follows. <br>";
                                $display = true;
                            } else if ($showwhat == "p100" && $person[3] == "100") {
                                echo "The students who got 100 points. <br>";
                                $display = true;
                            } else if ($showwhat == "dm0" && $person[3] == "0" && $person[2] == "Digital Media") {
                                echo "The Digital Media students who got 0 points. <br>";
                                $display = true;
                            } else if ($showwhat == "bydate") {
                                $test_date = strtotime($person[5]." ".$person[6]);
                                $yesterday = strtotime("yesterday 8:00am");
                                if ($test_date > $yesterday) {
                                    echo "The info for students who took the test after 8:00am of yesterday. <br>";
                                    $display = true;
                                }
                            } else if ($showwhat == "byname" && stripos($person[0], $student_name) !== false) {
                                echo "The info for students whose name is: ".$student_name;
                                $display = true;
                            }
                            
                            if ($display) {
                                $found++;
                                echo "<tr>";
                                echo "<td>".$found."</td>";
                                foreach ($person as $info) {
                                    echo "<td>".$info."</td>";
                                }
                                echo "</tr>";
                            }
                        }
                        echo "</table>";
                        echo "Total ".$found." records found.";
                    } else {
                        echo "No test results available.";
                    }
                } else {
                    echo "<p style='color:red;'>Invalid administrator credentials!</p>";
                }
            }
        ?>

    </body>
</html>
