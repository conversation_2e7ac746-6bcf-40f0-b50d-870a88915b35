<html>
    <head>
        <style>
            .error {color: #FF0000;}
        </style>

        <title>Lab 7</title>
    </head>

    <body>
        <div style="width:60%;margin:auto;">
            <h1>Grade Query System</h1>
            <form method="post" action="<?php echo $_SERVER['PHP_SELF']; ?>">
                <p><span class="error">* required field.</span></p>
                Query by:
                <select name="query">
                    <option value="Name" >Name</option>
                    <option value="Major" >Major</option>
                    <option value="Grade"  >Grade</option>
                </select> <br/><br/>
                Type the Name, Major or Grade that you want to search:
                <input type="text" name="search" value = "">
                <span class="error">*</span> <br/><br/>
                <input type=submit>
            </form>
            <hr/>

            <?php
                if (isset($_POST["search"]))
                {
                    $file = "Students-Grades-Info-2.txt";
                    $studentStr = file_get_contents($file);
                    $studentStr = trim($studentStr);

                    $studentList = explode("\n", $studentStr);
                    foreach ($studentList as $index=>$student)
                    {
                        $studentInfo[$index] = explode("\t", $student);
                    }

                    $queryType = $_POST["query"];
                    $searchTerm = $_POST["search"];
                    $nFound = 0;

                    echo "<table border='1'>";
                    echo "<tr>
                            <th>Name</th>
                            <th>Email</th>
                            <th>Major</th>
                            <th>Grade</th>
                            <th>IP</th>
                    </tr>";

                    foreach ($studentInfo as $student) {
                        if (($queryType == "Name" && $student[0] == $searchTerm) ||
                            ($queryType == "Major" && $student[2] == $searchTerm) ||
                            ($queryType == "Grade" && $student[3] == $searchTerm))
                        {
                            $nFound++;
                            echo "<tr>";
                            foreach ($student as $info) {
                                echo "<td>".$info."</td>";
                            }
                            echo "</tr>";
                        }
                    }
                    echo "</table>";
                    echo "There are totally ".$nFound." students matching your search.<br>";
                }
            ?>
        </div>
    </body>
</html>