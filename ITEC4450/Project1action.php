<html>
    <head>
        <title>Film Club Registration Results</title>
        <link rel="stylesheet" href="Project1.css">
        <style>
            body {
                text-align: center;
            }
            .result {
                background-color: floralwhite;
                display: inline-block;
                text-align: left;
                margin: 0 auto;
                padding: 20px;
                border-radius: 10px;
                box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
            }
            h1, h2, h3 {
                text-align: center;
            }
        </style>
        </style>
    </head>

    <body>
        <h1>Film Club Registration Results</h1>
        <div class="result">
            <?php
                echo "<h2>" .$_POST["firstname"]. "</h2>";
                echo "<h2> Thank you, for registering with the Film Club.</h2><br>";

                echo "Your Information";
                echo "First Name: " .$_POST["firstname"]. "<br>";
                echo "Last Name: " .$_POST["lastname"]. "<br>";
                echo "Email: " .$_POST["email"]. "<br>";
                echo "Academic Concentration: " .$_POST["concentration"]. "<br>";
                if(isset($_POST["gradyear"]) && !empty($_POST["gradyear"])) {
                    echo "Graduation Year: " .$_POST["gradyear"]. "<br>";
                }
                echo "<br>";

                echo "Your answers to the Film Knowledge Quiz: <br>";
                echo "Q1: " .$_POST["Q1"]. "<br>";
                echo "Q2: " .$_POST["Q2"]. "<br>";
                echo "Q3: " .$_POST["Q3"]. "<br>";
                echo "Q4: " .$_POST["Q4"]. "<br>";
                echo "Q5: " .$_POST["Q5"]. "<br><br>";

                echo "<h2>Your Film Knowledge Quiz score: </h2>";
                $score = 0;
                if ($_POST["Q1"] == "C")
                    $score += 20;
                if ($_POST["Q2"] == "C")
                    $score += 20;
                if ($_POST["Q3"] == "A")
                    $score += 20;
                if ($_POST["Q4"] == "B")
                    $score += 20;
                if ($_POST["Q5"] == "A")
                    $score += 20;
                echo "<h3> $score points</h3>";

                $level = "";
                if ($score < 60) {
                    $level = "Beginner";
                } elseif ($score >= 60 && $score <= 79) {
                    $level = "Intermediate";
                } else {
                    $level = "Advanced";
                }

                echo "<h2> Your Level: </h2>";
                echo "<span><h3> $level </h3></span>";
            ?>
        </div>
    </body>
</html>