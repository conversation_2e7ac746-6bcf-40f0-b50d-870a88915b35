<html>
    <head>
        <title>Lab 3</title>
    </head>

    <body>
        <?php
            $name = $_POST["name"];
            $birthDate = $_POST["birthDate"];
            $favColor = $_POST["favColor"];

            $birthTimestamp = strtotime($birthDate);
            $birthDayOfWeek = date("l", $birthTimestamp);
            $birthMonth = date("F", $birthTimestamp);
            $birthDay = date("d", $birthTimestamp);
            $birthYear = date("Y", $birthTimestamp);

            $thisYearBirthday = date("Y", time()) . "-" . date("m-d", $birthTimestamp);
            $thisYearBirthdayTimestamp = strtotime($thisYearBirthday);

            echo "<h1>Happy Birthday</h1>";
            echo "Hi " .$name. ", your birthday is on " . "<span style = 'color: " .$favColor. "; font-weight: bold;''>
                " .$birthDayOfWeek. ", " .$birthMonth. " " .$birthDay. ", " .$birthYear. "</span>.";

            function displayCalendar($dateTimestamp, $highlightColor) {
                $firstDay = date("m", $dateTimestamp). "/01/" .date("Y", $dateTimestamp);
                $day1 = strtotime($firstDay);
                $day1_Date = date("w", $day1);
                $highlightDate = date("d", $dateTimestamp);
                $monthName = date("F", $dateTimestamp);
                $year = date("Y", $dateTimestamp);

                echo "<h3>Calendar for " .$monthName. " " .$year. "</h3>";
                echo "<table border = '1' style = 'width: 300px;'>";
                echo "<tr> <th>Sun</th> <th>Mon</th> <th>Tue</th> <th>Wed</th> <th>Thu</th> <th>Fri</th> <th>Sat</th> </tr>";
                echo "<tr>";
                for ($i = 0; $i < $day1_Date; $i++) {
                    echo "<td> &nbsp; </td>";
                }
                do {
                    if (date("d", $day1) == $highlightDate)
                        echo "<td style = 'color: " .$highlightColor. "; font-weight: bold;'>" .date("d", $day1). "</td>";
                    else
                        echo "<td>" .date("d", $day1). "</td>";
                    if (date("w", $day1) == 6) {
                        echo "</tr>";
                        echo "<tr>";
                    }
                    $day1 = strtotime("+1 day", $day1);
                } while (date("m", $day1) == date("m", $dateTimestamp));
                echo "</tr>";
                echo "</table>";
            }

            displayCalendar($birthTimestamp, $favColor);
            displayCalendar($thisYearBirthdayTimestamp, $favColor);

            echo "<br>";
            echo "<img src = '../Images/birthday.jpg' style = 'width: 300px;'>";
        ?>
    </body>
</html>