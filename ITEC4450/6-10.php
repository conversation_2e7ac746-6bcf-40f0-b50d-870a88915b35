<html>
    <head>
        <title>6-10</title>
    </head>

    <body>
        <h1>This is a website containing a lot of stuff...</h1>
        <hr>

        <div id = "mydoc">
            <p> This is an article about a topic XZY... If you want to know more, click the button</p>
            <button type= "button" onclick = "LoadDoc()"> Load Article</button>
        </div>

        <script>
            function LoadDoc() {
                var xhttp = new XMLHttpRequest();
                xhttp.onreadystatechange = function () {
                    if (this.readyState == 4 && this.status == 200) {
                        document.getElementById("mydoc").innerHTML = this.response;
                    }
                }
                xhttp.open("GET", "AjaxFile.txt", true);
                xhttp.send();
            }
        </script>
        <hr>

        <!--------------- Stock Price --------------->
        <div>
            <h2>The most recent stock price for <span id = "myprice">Apple is: $140</span> </h2>
            <!-- <button type = "button" onclick = "LoadPrice()">Update Price</button> -->
        </div>

        <script>
            var alarm1 = setInterval(LoadPrice, 1000);
            function LoadPrice() {
                var xhttp = new XMLHttpRequest();
                xhttp.onreadystatechange = function () {
                    if (this.readyState == 4 && this.status == 200) {
                        document.getElementById("myprice").innerHTML = this.response;
                    }
                }
                xhttp.open("GET", "AjaxAction1.php", true);
                xhttp.send();
            }
        </script>
        <hr>

        <!--------------- Progress Bar --------------->
        <div>
            <h2>Loading something that takes a long time: <span id = "pvalue">0</span>% done</h2>
            <progress min="0" max="100" value="0" id="myprogress"></progress>
            <button type = "button" onclick = "LoadInfo()">Start Loading Info</button>
        </div>

        <script>
            function LoadInfo() {
                var alarm2 = setInterval(showProgress, 500);
            }

            function showProgress() {
                var xhttp = new XMLHttpRequest();
                xhttp.onreadystatechange = function () {
                    if (this.readyState == 4 && this.status == 200) {
                        document.getElementById("pvalue").innerHTML = this.response;
                    }
                }
                document.getElementById("myprogress").style.display = "block";
                var p = document.getElementById("pvalue").innerHTML;

                xhttp.open("GET", "AjaxAction2.php?cp="+p.toString(), true);
                xhttp.send();

                document.getElementById("myprogress").value = p;

                if (p >= 100) clearInterval(alarm2);
            }
        </script>
    </body>
</html>