<html>
    <head>
        <title>6-12</title>
        <style>
            .required {
                color: red;
            }
        </style>
    </head>

    <body>
        <?php
            function get_input($what, $from) {
                if (array_key_exists($what, $from))
                    return $from[$what];
                else
                    return null;
            }

            function clean_input($input) {
                if ($input == null)
                    return null;

                $input = trim($input); // ltrim, rtrim
                $input = htmlspecialchars($input);
                return $input;

            }

            $name = $email = $phone = "";
            $nameMSG = $emailMSG = $phoneMSG = "";

            if (get_input("submit", $_GET) != null) {
                $name = clean_input(get_input("name", $_GET));
                $email = clean_input(get_input("email", $_GET));
                $phone = clean_input(get_input("phone", $_GET));

                if (empty($name))
                    $nameMSG = "Name is required!";
                if (empty($email))
                    $emailMSG = "Email is required!";
                else {
                    if (!filter_var($email, FILTER_VALIDATE_EMAIL))
                    $emailMSG = "Email format is not valid!";
                }
                if (empty($phone))
                    $phoneMSG = "Phone is required!";
                else {
                    if (!filter_var($phone, FILTER_VALIDATE_INT))
                        $phoneMSG = "Phone number format is not valid!";
                }
            }
        ?>
        <form method="get" action="<?php echo $_SERVER['PHP_SELF']; ?>">
            Name: <input type="text" name="name" value="<?php echo $name;?>"> <span class="required">* <?php echo $nameMSG; ?> </span> <br>
            Email: <input type="text" name="email" value="<?php echo $email;?>"> <span class="required">* <?php echo $emailMSG; ?> </span> <br>
            Phone: <input type="text" name="phone" value="<?php echo $phone;?>"> <span class="required">* <?php echo $phoneMSG; ?> </span> <br>

            <?php
                $Q1 = $Q2 = $Q3 = "";
                $Q1 = clean_input(get_input("Q1", $_GET));
                $Q2 = clean_input(get_input("Q2", $_GET));
                $Q3 = clean_input(get_input("Q3", $_GET));

                function showAnswer() {
                    if (clean_input(get_input("showAnswer", $_GET)) == "Yes")
                        echo "<span style='color:red;'> <---This is the answer.</span>";
                }
            ?>

            <hr>
            <h1>Welcome to this online test</h1>
            Question 1> What is 1+1?<br>
            <input type=radio name=Q1 value=A <?php if ($Q1 == "A") echo "checked"; ?> >A. 0<br>
            <input type=radio name=Q1 value=B <?php if ($Q1 == "B") echo "checked"; ?> >B. 1<br>
            <input type=radio name=Q1 value=C <?php if ($Q1 == "C") echo "checked"; ?> >C. 2 <?php showAnswer(); ?> <br>
            <input type=radio name=Q1 value=D <?php if ($Q1 == "D") echo "checked"; ?> >D. 3<br>
            <input type=radio name=Q1 value=E <?php if ($Q1 == "E") echo "checked"; ?> >E. 4<br>
            <br>

            Question 2> What is 5-1?<br>
            <input type=radio name=Q2 value=A <?php if ($Q2 == "A") echo "checked";?> >A. 0<br>
            <input type=radio name=Q2 value=B <?php if ($Q2 == "B") echo "checked";?> >B. 1<br>
            <input type=radio name=Q2 value=C <?php if ($Q2 == "C") echo "checked";?> >C. 2<br>
            <input type=radio name=Q2 value=D <?php if ($Q2 == "D") echo "checked";?> >D. 3<br>
            <input type=radio name=Q2 value=E <?php if ($Q2 == "E") echo "checked";?> >E. 4 <?php showAnswer(); ?> <br>
            <br>

            Question 3> What is 2/2?<br>
            <input type=radio name=Q3 value=A <?php if ($Q3 == "A") echo "checked";?> >A. 0<br>
            <input type=radio name=Q3 value=B <?php if ($Q3 == "B") echo "checked";?> >B. 1 <?php showAnswer(); ?> <br>
            <input type=radio name=Q3 value=C <?php if ($Q3 == "C") echo "checked";?> >C. 2<br>
            <input type=radio name=Q3 value=D <?php if ($Q3 == "D") echo "checked";?> >D. 3<br>
            <input type=radio name=Q3 value=E <?php if ($Q3 == "E") echo "checked";?> >E. 4<br>
            <br>

            <input type=checkbox name=showAnswer value="Yes" <?php if (clean_input(get_input("showAnswer", $_GET)) == "Yes") echo "checked"; ?> >
            Show me the correct answers<br>
            <br>

            <input type="submit" name="submit" value="Submit">
            <input type="reset" name="reset" value="Reset">
        </form>

        <?php
            echo "Name: ".$name."<br>";
            echo "Email: ".$email."<br>";
            echo "Phone: ".$phone."<br>";

            $score = 0;
            $nq = 3; // number of questions
            $ppq = 100/$nq; // points per questions

            if ($Q1 == "C") $score += $ppq;
            if ($Q2 == "E") $score += $ppq;
            if ($Q3 == "B") $score += $ppq;
            echo "Your grade for this test is: ".round($score, 1)."<br>";
        ?>

        <hr>
        <?php
            $filename = "visitorNumber.txt";
            $nVisitor = file_get_contents($filename);

            if ($nVisitor == null || $nVisitor == '')
                $nVisitor = 0;

            $nVisitor ++;
            echo "This website has been visited by ".$nVisitor." people! <br>";
            file_put_contents($filename, $nVisitor, LOCK_EX);
            echo "The new visitor's IP is: ".$_SERVER["REMOTE_ADDR"]."<br>";

            echo "<hr>";
            $file = "log.txt";
            $vList = file_get_contents($file);
            $ip = $_SERVER["REMOTE_ADDR"];
            $vTime = $_SERVER["REQUEST_TIME"];
            $newVisitor = $ip."\t".date("M", $vTime)."\t".date("d", $vTime)."\t".date("Y", $vTime)." "
                ."\t".date("H", $vTime)."\t".date("i", $vTime)."\t".date("s", $vTime)."\n";
            file_put_contents($file, $newVisitor, FILE_APPEND|LOCK_EX);
            echo "<pre>".$vList.$newVisitor."</pre>";
        ?>
    </body>
</html>