<html>
    <head>
        <title>6-3</title>
    </head>

    <body>
        <?php
            echo "<h2>Lucky Day</h2>";
            $color = $_POST["favColor"];
            echo "Your lucky day is <span style = 'color: ".$color."; font-weight: bold;'>" .$_POST["luckyDay"]. "</span>.<br>";

            $day = strtotime($_POST["luckyDay"]);
            echo "Your lucky day is " .date("m/d/Y", $day). ".<br>";
            echo "Your lucky day is " .date("Y-M-d", $day). ".<br>";
            echo "Your lucky day is " .date("l, F j, Y", $day). ".<br>";

            echo "<h3>Current Time Table</h3>";
            echo "<table border = '1'>";
            echo "<tr> <td>Server Time</td> <td>" .date("h:i:sa"). "</td> </tr>";
            date_default_timezone_set("America/Los_Angeles");
            echo "<tr> <td>Los Angeles</td> <td>" .date("h:i:sa"). "</td> </tr>";
            date_default_timezone_set("Asia/Shanghai");
            echo "<tr> <td>Shanghai</td> <td>" .date("h:i:sa"). "</td> </tr>";
            date_default_timezone_set("America/New_York");
            echo "<tr> <td>New York</td> <td>" .date("h:i:sa"). "</td> </tr>";
            echo "</table>";
            echo "<br>";

            $d = strtotime("tomorrow");
            echo "Tomorrow is " .date("Y-m-d h:i:sA", $d). ".<br>";
            $d = strtotime("+1 week");
            echo "A week from now is " .date("Y-m-d h:i:sA", $d). ".<br>";
            $d = strtotime("+3 months");
            echo "3 months from now is " .date("Y-m-d h:i:sA", $d). ".<br>";

            $now = time();
            echo "<h3 style = 'color: " .$color. ";'>" .date("Y-m-d h:i:sA", $now). "</h3>";

            $firstDay = date("m", $now). "/01/" .date("Y", $now);
            $day1 = strtotime($firstDay);
            $day1_Date = date("w", $day1);
            //$startDate = date("d", $now);
            $now_Date = date("d", $now);

            echo "<table border = '1' style = 'width: 300px;'>";

            echo "<tr>";
            echo "<th>Sun</th> <th>Mon</th> <th>Tue</th> <th>Wed</th> <th>Thu</th> <th>Fri</th> <th>Sat</th>";
            echo "</tr>";

            echo "<tr>";
            for ($i = 0; $i < $day1_Date; $i++) {
                echo "<td> &nbsp; </td>";
            }
            do {
                if (date("d", $day1) == $now_Date)
                    echo "<td style = 'color: " .$color. "; font-weight: bold;'>" .date("d", $day1). "</td>";
                else
                    echo "<td>" .date("d", $day1). "</td>";
                if (date("w", $day1) == 6) {
                    echo "</tr>";
                    echo "<tr>";
                }
                $day1 = strtotime("+1 day", $day1);
            } while (date("d", $day1) > 1);
            echo "</tr>";

            echo "</table>";
        ?>
    </body>
</html>