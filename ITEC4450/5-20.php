<html>
    <head>
        <title>5-20</title>
    </head>

    <body>
        <h1>My First PHP Page</h1>
        <?php
            // echo
            echo "Hello World! <br>";

            # print
            print("<p> This is my first PHP class. <br>");
        ?>

        <h2>Variables</h2>
        <?php
            $school = "GGC";
            echo "I like $school. <br>";
            echo 'I like $school. <br>'; // single quote will not interpret the variable
            echo 'I like '.$school.'. <br>'; // concatenate

            $major = "I am an ITEC major.";
            echo "The string \"" . $major. "\" has " . strlen($major) . " characters.<br>";
            echo "The string \"" . $major. "\" has " . str_word_count($major) . " words.<br>";
            echo "<br><br>";

            $numStudents = 17;
            $totalStudents = 24;
            echo "$numStudents out of $totalStudents students took the start quiz. <br>";

            $rate = $numStudents / $totalStudents;
            echo "The rate is: $rate <br>";
            printf("The rate is: %d (in decimmal and %f (in float) <br>", $rate, $rate);
            
            $percent = round($rate, 4);
            printf("The rate is: %.2f%% <br>", $percent * 100);
        ?>

        <h2>Selection</h2>
        <?php
            echo "The current time is ".date("h:i:sa")." <br>"; // default time zone is UTC
            date_default_timezone_set("America/New_York"); // set to my timezone
            echo "The current time is ".date("h:i:sa")." <br>";

            $hour = date("H"); // 24-hours format of an hour
            if ($hour < "12") {
                echo "Good Morning! <br>";
            } else if ($hour < "18") { // else if and elseif are the same
                echo "Good Afternoon! <br>";
            } else {
                echo "Good Evening! <br>";
            }

            $day = date("l"); // lower case L, not number 1
            if ($day == "Tuesday" || $day == "Thursday") {
                echo "Enjoy you Web Developemnt class!! <br>";
            } else {
                echo "Enjoy $day! <br>";
            }

            $month = date("F"); // F - A full textual representation of a month
            if ($month == "May" || $month == "June" || $month == "July") {
                echo "Enjoy Summer!!! <br>";
            } else {
                echo "Enjoy School!!! <br>";
            }
        ?>

    </body>
</html>