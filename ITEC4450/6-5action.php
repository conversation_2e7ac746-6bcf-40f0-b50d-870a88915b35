<html>
    <head>
        <title>6-5</title>
    </head>

    <body>
        <h1>GGC Grill</h1>

        <?php
            $menu = array("Cheese Burger" => "6", "Chicken Burger" => "5", "Fries" => "1", "Coke" => "1");

            if (empty($_POST["select"])) {
                echo "You didn't order anything<br>";
            } else {
                $selected = $_POST["select"];
                echo "Thank you for order! <br><br>";
                echo "<table>";
                $subtotal = 0;
                foreach ($selected as $item) {
                    echo "<tr>";
                    echo "<td> $item </td>";
                    echo "<td style = 'text-align: right;'> $$menu[$item]</td>";
                    echo "</tr>";
                    $subtotal += $menu[$item];
                }

                echo "<tr> <td>&nbsp;</td> <td>&nbsp;</td>";

                echo "<tr>";
                echo "<td>Subtotal</td>";
                echo "<td style = 'text-align: right;'> $$subtotal </td>";
                echo "</tr>";

                echo "<tr>";
                echo "<td>Tax</td>";
                echo "<td style = 'text-align: right;'> $" .number_format($subtotal*0.06, 2). "</td>";
                echo "</tr>";

                echo "<tr>";
                echo "<td style = 'font-weight: bold'> Total </td>";
                echo "<td style = 'text-align: right'> $" .number_format($subtotal*1.06, 2). "</td>";
                echo "</tr>";

                echo "</table>";
            }
        ?>

    </body>
</html>
