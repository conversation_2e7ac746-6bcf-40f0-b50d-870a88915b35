<?php
function get_input($what, $from) {
    if (array_key_exists($what, $from))
        return $from[$what];
    else
        return null;
}

function clean_input($input) {
    if ($input == null)
        return null;

    $input = trim($input);
    $input = htmlspecialchars($input);
    return $input;
}

$name = $email = $major = "";
$Q1 = $Q2 = $Q3 = $Q4 = "";
$nameMSG = $emailMSG = "";

if (get_input("submit", $_POST) != null) {
    $name = clean_input(get_input("name", $_POST));
    $email = clean_input(get_input("email", $_POST));
    $major = clean_input(get_input("major", $_POST));
    $Q1 = clean_input(get_input("Q1", $_POST));
    $Q2 = clean_input(get_input("Q2", $_POST));
    $Q3 = clean_input(get_input("Q3", $_POST));
    $Q4 = clean_input(get_input("Q4", $_POST));

    if (empty($name))
        $nameMSG = "Name is required!";
    if (empty($email))
        $emailMSG = "Email is required!";
    else {
        if (!filter_var($email, FILTER_VALIDATE_EMAIL))
            $emailMSG = "Email format is not valid!";
    }
}

function showAnswer() {
    if (clean_input(get_input("showanswer", $_POST)) == "YES")
        echo "<span style='color:red;'> <---This is the correct answer.</span>";
}
?>