@import url('https://fonts.googleapis.com/css2?family=Oswald:wght@400;600&family=Roboto+Condensed:wght@300;400;700&display=swap');

.required {
    color: red;
    font-weight: bold;
}

body {
    font-family: 'Roboto Condensed', sans-serif;
    color: black;
    line-height: 1.6;
    max-width: 800px;
    margin: 0 auto;
    padding: 20px;
    background-color: antiquewhite;
    background-image:
            -webkit-radial-gradient(10% 20%, circle, rgba(255, 0, 0, 0.1) 0%, transparent 20%),
            -webkit-radial-gradient(90% 80%, circle, rgba(0, 0, 255, 0.1) 0%, transparent 20%);
    background-image:
            radial-gradient(circle at 10% 20%, rgba(255, 0, 0, 0.1) 0%, transparent 20%),
            radial-gradient(circle at 90% 80%, rgba(0, 0, 255, 0.1) 0%, transparent 20%);
}

h1 {
    font-family: '<PERSON>', sans-serif;
    color: black;
    text-align: center;
    margin-bottom: 25px;
    padding-bottom: 15px;
    font-size: 2.5em;
    letter-spacing: 1.5px;
    text-transform: uppercase;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
    position: relative;
}

fieldset {
    border: 1px solid #333;
    border-radius: 5px;
    padding: 25px;
    margin-bottom: 30px;
    background-color: floralwhite;
    position: relative;
    overflow: hidden;
}

fieldset:before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: pink;
}

legend {
    font-family: 'Oswald', sans-serif;
    font-weight: 600;
    color: black;
    padding: 0 15px;
    font-size: 1.4em;
    letter-spacing: 1px;
}

table {
    width: 100%;
    border-collapse: collapse;
}

th, td {
    padding: 12px;
    text-align: left;
    vertical-align: top;
}

th {
    color: black;
    width: 40%;
    font-size: 1.1em;
    font-weight: normal;
}

input[type="text"],
input[type="email"],
input[type="password"],
select {
    width: 100%;
    padding: 10px;
    border: 1px solid #ccc;
    border-radius: 4px;
    box-sizing: border-box;
    font-family: inherit;
    background-color: floralwhite;
    color: black;
    transition: all 0.3s;
}

input[type="text"]:focus,
input[type="email"]:focus,
input[type="password"]:focus,
select:focus {
    outline: none;
    border-color: pink;
    box-shadow: 0 0 5px rgba(255, 182, 193, 0.5);
}

input[type="radio"] {
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
    width: 16px;
    height: 16px;
    border: 2px solid #555;
    border-radius: 50%;
    margin-right: 10px;
    position: relative;
    top: 3px;
    transition: all 0.2s;
}

input[type="radio"]:checked {
    background-color: black;
}

label {
    display: block;
    margin-bottom: 12px;
    cursor: pointer;
    transition: color 0.2s;
}

label:hover {
    color: mediumpurple;
}

input[type="submit"],
input[type="reset"] {
    padding: 10px 20px;
    background-color: pink;
    color: black;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.3s;
    margin-right: 10px;
}

input[type="submit"]:hover,
input[type="reset"]:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 10px rgba(0, 0, 0, 0.4);
}

p {
    margin: 18px 0;
    font-size: 1.1em;
}