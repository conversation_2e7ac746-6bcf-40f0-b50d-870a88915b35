<html>
    <head>
        <title>6-17 Two</title>
    </head>

    <body>
        <h1>Query System</h1>
        <form method="post" action="<?php echo $_SERVER['PHP_SELF']; ?>">
            Please provide your name:<br/>
            <input type="text" name="name"><br/>
            <input type="submit" name="submit">
        </form>

        <?php
            if (isset($_POST["submit"])) {
                $file = "SignUpInfo.txt";
                $infoStr = file_get_contents($file);
                $infoStr = trim($infoStr);

                $infoList = explode("\n", $infoStr); // 1D array
                foreach ($infoList as $index=>$line)
                    $personInfo[$index] = explode("\t", $line); // 2D array
                $found = 0;

                echo "<table border='1'>";
                    echo "<tr>
                        <th>No.</th>
                        <th>Name</th>
                        <th>Email</th>
                        <th>Major</th>
                        <th>IP</th>
                        <th>Date</th>
                        <th>Time</th>
                        <th>ID</th>
                    </tr>";
                    
                foreach ($personInfo as $people) {
                    if ($people[0] == $_POST["name"] || $_POST["name"] == "*") {
                        $found++;
                        echo "<tr>";
                            echo "<td>".$found."</td>";
                            foreach ($people as $info)
                                echo "<td>".$info."</td>";
                        echo "</tr>";
                    }
                }
                echo "</table>";
                echo "Total ".$found." people are found. <br>";
            }
        ?>
    </body>
</html>
